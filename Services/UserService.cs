// Services/UserService.cs
using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace backend.Services
{
    public class UserService : IUserService
    {
        private readonly IHttpContextAccessor _contextAccessor;

        public UserService(IHttpContextAccessor contextAccessor)
        {
            _contextAccessor = contextAccessor;
        }

        public string? GetUserId()
        {
            return _contextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.NameIdentifier);
        }

        public string? GetUserRole()
        {
            return _contextAccessor.HttpContext?.User.FindFirstValue(ClaimTypes.Role);
        }
    }
}
