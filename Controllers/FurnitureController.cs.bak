using Microsoft.AspNetCore.Mvc;
using backend.Models;
using backend.Repositories;
using backend.DTOs;
using System.Threading.Tasks;
using System.Linq;
using System;
using System.Collections.Generic;

namespace backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FurnitureController : ControllerBase
    {
        private readonly ISingletonRepository<PricingData> _pricingRepository;

        public FurnitureController(ISingletonRepository<PricingData> pricingRepository)
        {
            _pricingRepository = pricingRepository;
        }

        [HttpGet("pricing")]
        public async Task<IActionResult> GetPricing()
        {
            PricingData pricing;
            try
            {
                pricing = await _pricingRepository.GetAsync();
            }
            catch (Exception ex)
            {
                // Log the error and return default pricing data if deserialization fails
                Console.WriteLine($"Error deserializing pricing data: {ex.Message}");
                pricing = null;
            }

            if (pricing == null)
            {
                // Return default pricing data if none exists
                pricing = new PricingData
                {
                    GeneralOutcomeArmchair = 0,
                    GeneralOutcomeBergere = 0,
                    ProfitRate = 0,
                    FixedFabricAmount = 0,

                    // Initialize SeatOptions with default values
                    SeatOptions = new Dictionary<string, PriceAndFabric>
                    {
                        { "OturumSeçenek 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "OturumSeçenek 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "OturumSeçenek 4", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize ArmrestBase with default values
                    ArmrestBase = new Dictionary<string, PriceAndFabric>
                    {
                        { "1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "4", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "5", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize LowerFrame with default values
                    LowerFrame = new Dictionary<string, decimal>
                    {
                        { "Ceviz", 0 },
                        { "Beyaz", 0 },
                        { "Ekru", 0 },
                        { "Gri", 0 },
                        { "Siyah", 0 },
                        { "Sarı Eskitme", 0 },
                        { "Siyah Eskitme", 0 },
                        { "Ceviz Eskitme", 0 }
                    },

                    // Initialize LegOptions with default values
                    LegOptions = InitializeLegOptions(),

                    // Initialize BackPillows with default values
                    BackPillows = new Dictionary<string, PriceAndFabric>
                    {
                        { "Silikon Elyaf", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Sünger", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize Cushions with default values
                    Cushions = new Dictionary<string, PriceAndFabric>
                    {
                        { "Sünger", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Silikon Elyaf", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize BergereOptions with default values
                    BergereOptions = new Dictionary<string, PriceAndFabric>
                    {
                        { "Bergere 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Bergere 2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Bergere 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize Fabrics with default values
                    Fabrics = new Dictionary<string, Dictionary<string, decimal>>
                    {
                        { "Kartela 1", new Dictionary<string, decimal>
                            {
                                { "1", 0 }, { "2", 0 }, { "3", 0 },
                                { "4", 0 }, { "5", 0 }, { "6", 0 },
                                { "7", 0 }, { "8", 0 }, { "9", 0 }
                            }
                        }
                    }
                };
            }

            return Ok(pricing);
        }

        [HttpPut("pricing")]
        public async Task<IActionResult> UpdatePricing([FromBody] PricingDataDto dto)
        {
            if (dto == null)
            {
                return BadRequest("Invalid pricing data");
            }

            try
            {
                var existingPricing = await _pricingRepository.GetAsync();
                var pricing = new PricingData
                {
                    Id = existingPricing?.Id, // Preserve existing ID
                    GeneralOutcomeArmchair = dto.GeneralOutcomeArmchair,
                    GeneralOutcomeBergere = dto.GeneralOutcomeBergere,
                    ProfitRate = dto.ProfitRate,

                    // Map all fields from DTO
                    SeatOptions = dto.SeatOptions ?? new Dictionary<string, PriceAndFabric>(),
                    ArmrestBase = dto.ArmrestBase ?? new Dictionary<string, PriceAndFabric>(),
                    LowerFrame = dto.LowerFrame ?? new Dictionary<string, decimal>(),
                    LegOptions = dto.LegOptions ?? new Dictionary<string, Dictionary<string, decimal>>(),
                    BackPillows = dto.BackPillows ?? new Dictionary<string, PriceAndFabric>(),
                    Cushions = dto.Cushions ?? new Dictionary<string, PriceAndFabric>(),
                    BergereOptions = dto.BergereOptions ?? new Dictionary<string, PriceAndFabric>(),
                    Fabrics = dto.Fabrics ?? new Dictionary<string, Dictionary<string, decimal>>(),

                    // Map legacy fields if they exist
                    FixedCost = dto.FixedCost,
                    SeatWithNoEar = dto.SeatWithNoEar ?? new Dictionary<string, PriceAndFabric>(),
                    SeatWithEar = dto.SeatWithEar ?? new Dictionary<string, PriceAndFabric>(),
                    Arms = dto.Arms ?? new Dictionary<string, PriceAndFabric>(),
                    ArmExtensions = dto.ArmExtensions ?? new Dictionary<string, PriceAndFabric>(),
                    SeatSubOptions = dto.SeatSubOptions ?? new Dictionary<string, PriceAndFabric>(),
                    ArmrestPapel = dto.ArmrestPapel ?? new Dictionary<string, PriceAndFabric>(),
                    ArmrestKlapa = dto.ArmrestKlapa ?? new Dictionary<string, PriceAndFabric>(),
                    LowerFrameColors = dto.LowerFrameColors ?? new Dictionary<string, decimal>(),
                    LegColors = dto.LegColors ?? new Dictionary<string, decimal>()
                };

                await _pricingRepository.UpdateAsync(pricing);
                return Ok(new { message = "Pricing updated successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error updating pricing data", error = ex.Message });
            }
        }

        private Dictionary<string, Dictionary<string, decimal>> InitializeLegOptions()
        {
            var legTypes = new[] { "1", "2", "6", "7", "8", "9", "10", "11", "12" };
            var woodenColors = new[] { "Ceviz", "Beyaz", "Ekru", "Gri", "Antrasit", "Sarı Eskitme", "Gri Eskitme", "Ceviz Eskitme" };
            var metalColors = new[] { "Bronz", "Gold", "Nikel" };

            var legOptions = new Dictionary<string, Dictionary<string, decimal>>();

            foreach (var legType in legTypes)
            {
                var colors = new Dictionary<string, decimal>();
                
                // Add wooden colors for all leg types
                foreach (var color in woodenColors)
                {
                    colors[color] = 0;
                }

                // Add metal colors for all leg types
                foreach (var color in metalColors)
                {
                    colors[color] = 0;
                }

                legOptions[legType] = colors;
            }

            return legOptions;
        }

        [HttpPost("pricing/reset")]
        public async Task<IActionResult> ResetPricingData()
        {
            try
            {
                // Cast to concrete type to access DeleteAllAsync method
                var pricingRepo = _pricingRepository as PricingDataRepository;
                if (pricingRepo != null)
                {
                    await pricingRepo.DeleteAllAsync();
                }

                // Create new default pricing data
                var newPricing = new PricingData
                {
                    GeneralOutcomeArmchair = 0,
                    GeneralOutcomeBergere = 0,
                    ProfitRate = 0,
                    FixedFabricAmount = 0,

                    // Initialize SeatOptions with default values
                    SeatOptions = new Dictionary<string, PriceAndFabric>
                    {
                        { "OturumSeçenek 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "OturumSeçenek 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "OturumSeçenek 4", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize ArmrestBase with default values
                    ArmrestBase = new Dictionary<string, PriceAndFabric>
                    {
                        { "1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "4", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "5", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize LowerFrame with default values
                    LowerFrame = new Dictionary<string, decimal>
                    {
                        { "Ceviz", 0 },
                        { "Beyaz", 0 },
                        { "Ekru", 0 },
                        { "Gri", 0 },
                        { "Siyah", 0 },
                        { "Sarı Eskitme", 0 },
                        { "Siyah Eskitme", 0 },
                        { "Ceviz Eskitme", 0 }
                    },

                    // Initialize LegOptions with default values
                    LegOptions = InitializeLegOptions(),

                    // Initialize BackPillows with default values
                    BackPillows = new Dictionary<string, PriceAndFabric>
                    {
                        { "Silikon Elyaf", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Sünger", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize Cushions with default values
                    Cushions = new Dictionary<string, PriceAndFabric>
                    {
                        { "Sünger", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Silikon Elyaf", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Kırlent 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize BergereOptions with default values
                    BergereOptions = new Dictionary<string, PriceAndFabric>
                    {
                        { "Bergere 1", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Bergere 2", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } },
                        { "Bergere 3", new PriceAndFabric { Price = 0, FabricAmount = 0, FixedFabricAmount = 0 } }
                    },

                    // Initialize Fabrics with default values
                    Fabrics = new Dictionary<string, Dictionary<string, decimal>>()
                };

                // Initialize all 12 kartelas
                for (int kartela = 1; kartela <= 12; kartela++)
                {
                    var colorCount = kartela == 12 ? 6 : kartela == 8 ? 10 : 9;
                    var colors = new Dictionary<string, decimal>();
                    for (int color = 1; color <= colorCount; color++)
                    {
                        colors[color.ToString()] = 0;
                    }
                    newPricing.Fabrics[$"Kartela {kartela}"] = colors;
                }

                await _pricingRepository.UpdateAsync(newPricing);
                return Ok(new { message = "Pricing data reset successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = "Error resetting pricing data", error = ex.Message });
            }
        }
    }
}