// Controllers/StoreSettingsController.cs
using backend.Models;
using backend.Repositories;
using backend.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using System.Linq;
using System.Security.Claims;

namespace backend.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class StoreSettingsController : ControllerBase
    {
        private readonly IRepository<StoreSetting> _settingRepository;
        private readonly IUserService _userService;

        public StoreSettingsController(IRepository<StoreSetting> settingRepository, IUserService userService)
        {
            _settingRepository = settingRepository;
            _userService = userService;
        }

        // GET: api/StoreSettings
        [Authorize(Roles = "store")]
        [HttpGet]
        public async Task<ActionResult<StoreSetting>> GetStoreSettings()
        {
            var storeId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (storeId == null)
            {
                return Unauthorized();
            }
            var setting = await _settingRepository.GetAsync("StoreId", storeId);
            if (setting == null)
            {
                return NotFound();
            }
            return Ok(setting);
        }

        // GET: api/StoreSettings/user/{userId}
        [Authorize(Roles = "admin")]
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<StoreSetting>> GetStoreSettingsByUserId(string userId)
        {
            var setting = await _settingRepository.GetAsync("StoreId", userId);
            if (setting == null)
            {
                return NotFound();
            }
            return Ok(setting);
        }

        // PUT: api/StoreSettings
        [Authorize(Roles = "store")]
        [HttpPut]
        public async Task<IActionResult> PutStoreSetting(StoreSetting setting)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var existingSettings = await _settingRepository.GetAllAsync();
            var existingSetting = existingSettings.FirstOrDefault();
            if (existingSetting == null)
            {
                return NotFound();
            }

            // Update fields
            existingSetting.CompanyName = setting.CompanyName;
            existingSetting.StoreId = userId;
            existingSetting.TaxNumber = setting.TaxNumber;
            existingSetting.OwnerName = setting.OwnerName;
            existingSetting.OwnerPhone = setting.OwnerPhone;
            existingSetting.StoreAddress = setting.StoreAddress;
            existingSetting.StorePhone = setting.StorePhone;
            existingSetting.ManagerName = setting.ManagerName;
            existingSetting.ManagerPhone = setting.ManagerPhone;
            existingSetting.ShippingCompany = setting.ShippingCompany;
            existingSetting.ShippingPhone = setting.ShippingPhone;
            existingSetting.ProfitMargin = setting.ProfitMargin;

            await _settingRepository.UpdateAsync(existingSetting.Id!, existingSetting);

            return NoContent();
        }
    }
}
