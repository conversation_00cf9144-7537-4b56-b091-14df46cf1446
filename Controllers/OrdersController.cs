// Controllers/OrdersController.cs

using backend.Models;
using backend.Repositories;
using backend.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Text.Json;
using backend.DTOs;

namespace backend.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class OrdersController : ControllerBase
    {
        private readonly IOrderRepository _orderRepository;
        private readonly ISingletonRepository<PricingData> _pricingRepository;
        private readonly IUserService _userService;

        public OrdersController(
            ISingletonRepository<PricingData> pricingRepository,
            IUserService userService,
            IOrderRepository orderRepository)
        {
            _orderRepository = orderRepository;
            _pricingRepository = pricingRepository;
            _userService = userService;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<OrderResponseDto>>> GetOrders()
        {
            var userRole = _userService.GetUserRole();
            var userId = _userService.GetUserId();

            if (userId == null)
            {
                return Unauthorized();
            }

            var orders = await _orderRepository.GetAllAsync();
            if (userRole == "admin")
            {

                var orderDtos = orders.Select(o => MapOrderToDto(o));
                return Ok(orderDtos);
            }
            else
            {
                var userOrders = orders.Where(o => o.UserId == userId);
                var orderDtos = userOrders.Select(o => MapOrderToDto(o));
                return Ok(orderDtos);
            }
        }

        [HttpGet("cart")]
        public async Task<ActionResult<Cart>> GetCart()
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var cart = await _orderRepository.GetCartAsync(userId);
            return Ok(cart);
        }

        [HttpPost("cart")]
        public async Task<ActionResult<Cart>> AddToCart(CartItem item)
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var cart = await _orderRepository.AddToCartAsync(userId, item);
            return Ok(cart);
        }

        [HttpDelete("cart/{itemId}")]
        public async Task<ActionResult<Cart>> RemoveFromCart(string itemId)
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var cart = await _orderRepository.RemoveFromCartAsync(userId, itemId);
            return Ok(cart);
        }

        [HttpPut("cart/{itemId}")]
        public async Task<ActionResult<Cart>> UpdateCartItemQuantity(string itemId, [FromBody] UpdateQuantityRequestDto request)
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var cart = await _orderRepository.UpdateCartItemQuantityAsync(userId, itemId, request.Quantity);
            return Ok(cart);
        }

        [HttpDelete("cart")]
        public async Task<ActionResult> ClearCart()
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            await _orderRepository.ClearCartAsync(userId);
            return Ok();
        }

        [HttpPost("place-order")]
        public async Task<ActionResult<OrderResponseDto>> PlaceOrder([FromBody] PlaceOrderRequestDto request)
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            // Get user's cart
            var cart = await _orderRepository.GetCartAsync(userId);
            if (cart == null || !cart.Items.Any())
            {
                return BadRequest("Cart is empty");
            }

            // Create order from cart items
            var order = new Order
            {
                UserId = userId,
                OrderDate = DateTime.UtcNow,
                Status = "Pending",
                Items = cart.Items.Select(i => new OrderItem
                {
                    Quantity = i.Quantity,
                    DesignJson = JsonSerializer.Serialize(i.Design)
                }).ToList()
            };

            // Use the total price from frontend
            order.TotalPrice = request.TotalPrice;

            // Optional: Validate the price if requested
            if (request.ValidatePrice)
            {
                var calculatedPrice = await CalculateTotalPrice(order.Items);
                
                // Allow a small tolerance for floating point differences (0.01 currency units)
                if (Math.Abs(calculatedPrice - request.TotalPrice) > 0.01m)
                {
                    return BadRequest($"Price mismatch. Expected: {calculatedPrice}, Received: {request.TotalPrice}");
                }
            }

            // Save order
            await _orderRepository.CreateAsync(order);

            // Clear the cart
            await _orderRepository.ClearCartAsync(userId);

            var responseDto = MapOrderToDto(order);
            return CreatedAtAction(nameof(GetOrders), new { id = order.Id }, responseDto);
        }

        private async Task<decimal> CalculateTotalPrice(ICollection<OrderItem> items)
        {
            decimal totalPrice = 0;
            var pricingData = await _pricingRepository.GetAsync();

            if (pricingData == null)
            {
                throw new InvalidOperationException("Pricing data not found");
            }

            foreach (var item in items)
            {
                var designJson = item.DesignJson ?? "{}";
                var design = JsonSerializer.Deserialize<Dictionary<string, object>>(designJson);
                if (design == null)
                {
                    continue; // Skip this item if design couldn't be deserialized
                }

                decimal itemPrice = 0;
                string selectedFabric = "";

                // Get fabric first since we need it for calculations
                if (design.TryGetValue("fabric", out var fabricObj) && fabricObj is string fabric)
                {
                    selectedFabric = fabric;
                }

                // Get product type from design
                string productType = "Armchair"; // Default
                if (design.TryGetValue("type", out var typeObj) && typeObj is string type)
                {
                    productType = type;
                }

                // Add General Outcome Cost
                if (productType == "Armchair")
                {
                    itemPrice += pricingData.GeneralOutcomeArmchair;
                }
                else if (productType == "Bergere")
                {
                    itemPrice += pricingData.GeneralOutcomeBergere;
                }

                // Parse armrest selections from design
                string[] armrestSelections = new string[] { };
                if (design.TryGetValue("armrest", out var armrestObj) && 
                    armrestObj is JsonElement armrestElement &&
                    armrestElement.TryGetProperty("sub", out var subElement))
                {
                    string subValue = subElement.GetString() ?? "";
                    if (!string.IsNullOrEmpty(subValue) && subValue != "BOŞ")
                    {
                        armrestSelections = subValue.Split('-');
                    }
                }

                // Calculate armrest prices
                foreach (var selection in armrestSelections)
                {
                    if (selection == "4" && pricingData.ArmrestBase.ContainsKey("4"))
                    {
                        var baseData = pricingData.ArmrestBase["4"];
                        decimal fabricPrice = GetFabricPrice(pricingData, selectedFabric);
                        itemPrice += baseData.Price + (baseData.FabricAmount * fabricPrice);
                    }
                    else if (selection == "3" && pricingData.ArmrestPapel.ContainsKey("3"))
                    {
                        var papelData = pricingData.ArmrestPapel["3"];
                        decimal fabricPrice = GetFabricPrice(pricingData, selectedFabric);
                        itemPrice += papelData.Price + (papelData.FabricAmount * fabricPrice);
                    }
                    else if (selection == "5" && pricingData.ArmrestKlapa.ContainsKey("5"))
                    {
                        var klapaData = pricingData.ArmrestKlapa["5"];
                        decimal fabricPrice = GetFabricPrice(pricingData, selectedFabric);
                        itemPrice += klapaData.Price + (klapaData.FabricAmount * fabricPrice);
                    }
                }

                // Parse seat selections from design
                string[] seatSelections = new string[] { };
                if (design.TryGetValue("seat", out var seatObj) && 
                    seatObj is JsonElement seatElement)
                {
                    if (seatElement.TryGetProperty("option", out var optionElement))
                    {
                        string optionValue = optionElement.GetString() ?? "";
                        if (!string.IsNullOrEmpty(optionValue) && optionValue != "BOŞ")
                        {
                            seatSelections = new[] { optionValue };
                        }
                    }
                    if (seatElement.TryGetProperty("sub", out var seatSubElement))
                    {
                        string subValue = seatSubElement.GetString() ?? "";
                        if (!string.IsNullOrEmpty(subValue) && subValue != "BOŞ")
                        {
                            seatSelections = seatSelections.Concat(subValue.Split('-')).ToArray();
                        }
                    }
                }

                // Calculate seat prices
                foreach (var selection in seatSelections)
                {
                    if (pricingData.SeatOptions.ContainsKey(selection))
                    {
                        var seatData = pricingData.SeatOptions[selection];
                        decimal fabricPrice = GetFabricPrice(pricingData, selectedFabric);
                        itemPrice += seatData.Price + (seatData.FabricAmount * fabricPrice);
                    }
                    else if (pricingData.SeatSubOptions.ContainsKey(selection))
                    {
                        var seatSubData = pricingData.SeatSubOptions[selection];
                        decimal fabricPrice = GetFabricPrice(pricingData, selectedFabric);
                        itemPrice += seatSubData.Price + (seatSubData.FabricAmount * fabricPrice);
                    }
                }

                // Apply Fixed Cost and Profit Rate
                itemPrice = (itemPrice + pricingData.FixedCost) * (1 + pricingData.ProfitRate / 100);

                // Multiply by Quantity
                totalPrice += itemPrice * item.Quantity;
            }

            return totalPrice;
        }

        private decimal GetFabricPrice(PricingData pricingData, string fabricKey)
        {
            if (string.IsNullOrEmpty(fabricKey) || !pricingData.Fabrics.ContainsKey(fabricKey))
            {
                return 0m;
            }
            return pricingData.Fabrics[fabricKey].Values.FirstOrDefault();
        }

        private OrderResponseDto MapOrderToDto(Order order)
        {
            return new OrderResponseDto
            {
                Id = order.Id,
                UserId = order.UserId,
                OrderDate = order.OrderDate,
                Status = order.Status,
                StoreName = order.StoreName,
                TotalPrice = order.TotalPrice,
                Items = order.Items?.Select(i => new OrderItemResponseDto
                {
                    Quantity = i.Quantity,
                    DesignJson = i.DesignJson
                }).ToList() ?? new List<OrderItemResponseDto>()
            };
        }

        [HttpGet("dashboard-stats")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult<DashboardStats>> GetDashboardStats()
        {
            var stats = await _orderRepository.GetDashboardStatsAsync();
            return Ok(stats);
        }

        [HttpGet("saved-designs")]
        public async Task<ActionResult<IEnumerable<SavedDesign>>> GetSavedDesigns()
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            var designs = await _orderRepository.GetSavedDesignsAsync(userId);
            return Ok(designs);
        }

        [HttpGet("saved-designs/{id}")]
        public async Task<ActionResult<SavedDesign>> GetSavedDesign(string id)
        {
            var design = await _orderRepository.GetSavedDesignByIdAsync(id);
            if (design == null)
            {
                return NotFound();
            }

            var userId = _userService.GetUserId();
            if (userId != design.UserId)
            {
                return Forbid();
            }

            return Ok(design);
        }

        [HttpPost("saved-designs")]
        public async Task<ActionResult> SaveDesign(SavedDesign design)
        {
            var userId = _userService.GetUserId();
            if (userId == null)
            {
                return Unauthorized();
            }

            await _orderRepository.SaveDesignAsync(userId, design);
            return Ok();
        }

        [HttpDelete("saved-designs/{id}")]
        public async Task<ActionResult> DeleteSavedDesign(string id)
        {
            var design = await _orderRepository.GetSavedDesignByIdAsync(id);
            if (design == null)
            {
                return NotFound();
            }

            var userId = _userService.GetUserId();
            if (userId != design.UserId)
            {
                return Forbid();
            }

            await _orderRepository.DeleteSavedDesignAsync(id);
            return Ok();
        }

        [HttpPut("saved-designs/{id}")]
        public async Task<ActionResult> UpdateSavedDesign(string id, SavedDesign design)
        {
            var existingDesign = await _orderRepository.GetSavedDesignByIdAsync(id);
            if (existingDesign == null)
            {
                return NotFound();
            }

            var userId = _userService.GetUserId();
            if (userId != existingDesign.UserId)
            {
                return Forbid();
            }

            await _orderRepository.UpdateSavedDesignAsync(id, design);
            return Ok();
        }

        [HttpPut("{id}/approve")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> ApproveOrder(string id)
        {
            var order = await _orderRepository.GetByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            order.Status = "Approved";
            await _orderRepository.UpdateAsync(id, order);
            return Ok();
        }

        [HttpPut("{id}/start-production")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> StartProduction(string id)
        {
            var order = await _orderRepository.GetByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            order.Status = "In Production";
            await _orderRepository.UpdateAsync(id, order);
            return Ok();
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "admin")]
        public async Task<ActionResult> CompleteOrder(string id)
        {
            var order = await _orderRepository.GetByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            order.Status = "Completed";
            await _orderRepository.UpdateAsync(id, order);
            return Ok();
        }

        [HttpPut("{id}/cancel")]
        public async Task<ActionResult> CancelOrder(string id)
        {
            var order = await _orderRepository.GetByIdAsync(id);
            if (order == null)
            {
                return NotFound();
            }

            order.Status = "Cancelled";
            await _orderRepository.UpdateAsync(id, order);
            return Ok();
        }
    }

    // DTO Classes
    public class OrderCreateDto
    {
        public List<OrderItemDto> Items { get; set; } = new List<OrderItemDto>();
    }

    public class OrderItemDto
    {
        public int Quantity { get; set; }
        public string? DesignJson { get; set; }
    }

    public class OrderResponseDto
    {
        public string? Id { get; set; }
        public string? UserId { get; set; }
        public DateTime OrderDate { get; set; }
        public string? Status { get; set; }
        public string? StoreName { get; set; }
        public decimal TotalPrice { get; set; }
        public List<OrderItemResponseDto> Items { get; set; } = new List<OrderItemResponseDto>();
    }

    public class OrderItemResponseDto
    {
        public int Quantity { get; set; }
        public string? DesignJson { get; set; }
    }
}
