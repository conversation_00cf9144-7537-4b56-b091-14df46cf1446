export type User = {
  id: string
  username: string
  role: 'admin' | 'store'
  storeName?: string
  isActive: boolean
  // Basic contact info
  email?: string
}

export type PlaceOrderRequest = {
  totalPrice: number
  storePrice: number
  adminPrice: number
  validatePrice?: boolean
}

export type Product = {
  id: string
  name: string
  description: string
  price: number
  image: string
}

export type Order = {
  id: string
  userId: string
  products: { productId: string; quantity: number }[]
  totalPrice: number
  status: 'pending' | 'approved' | 'in_transit' | 'delivered'
  createdAt: string
  estimatedDelivery: string
}

// Enhanced order structure for complete order data
export type EnhancedOrder = {
  id?: string
  // Customer information
  customerInfo: {
    name: string
    surname: string
    email?: string
    phone?: string
    address?: string
  }
  // Order items with complete pricing
  items: Array<{
    design: {
      legs: string
      baseFrame: string
      woodVeneer: string
      armrest: {
        main: string
        sub: string
        mainAhsap: boolean
      }
      seat: {
        option: string
        sub: string
      }
      sharedFabric: {
        option: string
        color: string
      }
      woodVeneerFabric: {
        option: string
        color: string
      }
      legFabric: {
        option: string
        color: string
      }
      mainAhsapFabric: {
        option: string
        color: string
      }
      model: string
      armchairType?: string
      backPillow?: string
      cushions?: Array<{
        cushionType: string
        fabric: string
        color: string
      }>
      barcode: string
    }
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  // Order totals
  orderTotal: number
  subtotal?: number
  tax?: number
  // Order metadata
  status?: 'pending' | 'approved' | 'in_transit' | 'delivered'
  createdAt?: string
  estimatedDelivery?: string
  notes?: string
  deliveryDate?: string
}

export type PriceFormData = {
  sofaType: {
    threeSeater: number
    armchair: number
  }
  fabric: Record<string, number>
  backPillow: {
    none: number
    filled: number
    foam: number
  }
  cushions: {
    none: number
    small: number
    medium: number
    large: number
  }
  armrest: Record<string, number>
  seat: Record<string, number>
  lowerFrame: Record<string, number>
  lowerFrameColors: Record<string, number>
  legs: Record<string, number>
}

export type StoreSetting = {
  id?: string
  storeId?: string
  companyName: string
  taxNumber?: string
  ownerName?: string
  ownerPhone?: string
  storeAddress?: string
  storePhone?: string
  managerName?: string
  managerPhone?: string
  shippingCompany?: string
  shippingPhone?: string
  profitMargin: number
}
