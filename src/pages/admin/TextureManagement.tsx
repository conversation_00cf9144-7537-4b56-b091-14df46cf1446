import React, { useState, useEffect } from 'react'
import { use<PERSON>orm, SubmitHandler, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiImage, FiSave, FiUpload } from 'react-icons/fi'
import { Button } from '@/components/common/Button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card'
import { AdminSidebar } from '@/components/layout/AdminSidebar'
import { Input } from '@/components/common/Input'
import { api } from '@/services/api'

const textureSchema = z.object({
  name: z.string().min(1, 'Texture name is required'),
  mtzFile: z.instanceof(File).optional(),
})

type TextureFormData = {
  textures: z.infer<typeof textureSchema>[]
}

export default function TextureManagement() {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { register, control, handleSubmit, formState: { errors }, setValue } = useForm<TextureFormData>({
    resolver: zodResolver(z.object({ textures: z.array(textureSchema) })),
    defaultValues: {
      textures: [],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: "textures",
  })

  useEffect(() => {
    // Fetch initial data from API
    const fetchData = async () => {
      try {
        const response = await api.get('/textures')
        setValue('textures', response.data)
      } catch (error) {
        console.error('Error fetching textures:', error)
      }
    }
    fetchData()
  }, [setValue])

  const onSubmit: SubmitHandler<TextureFormData> = async (data) => {
    setIsSubmitting(true)
    try {
      await api.put('/textures', data.textures)
      alert('Textures updated successfully')
    } catch (error) {
      console.error('Error updating textures:', error)
      alert('Failed to update textures. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleAddTexture = () => {
    append({ name: '', mtzFile: undefined })
  }

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <main className="flex-1 p-8 overflow-y-auto">
        <h1 className="text-4xl font-bold text-foreground mb-8 flex items-center">
          <FiImage className="mr-2 text-primary" />
          Texture Management
        </h1>
        <Card className="w-full mx-auto shadow-lg">
          <CardHeader className="bg-primary/10">
            <CardTitle className="text-2xl text-primary">Manage Textures</CardTitle>
          </CardHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-8">
              {fields.map((field, index) => (
                <div key={field.id} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <Input
                      label="Texture Name"
                      {...register(`textures.${index}.name` as const)}
                      error={errors.textures?.[index]?.name?.message}
                    />
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">MTZ File</label>
                      <input
                        type="file"
                        accept=".mtz"
                        {...register(`textures.${index}.mtzFile` as const)}
                        className="mt-1 block w-full text-sm text-gray-500
                          file:mr-4 file:py-2 file:px-4
                          file:rounded-full file:border-0
                          file:text-sm file:font-semibold
                          file:bg-primary file:text-white
                          hover:file:bg-primary-dark"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        onClick={() => remove(index)}
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              <Button type="button" variant="outline" onClick={handleAddTexture}>
                Add Texture
              </Button>
            </CardContent>
            <CardFooter className="bg-primary/5">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full md:w-auto flex items-center justify-center"
              >
                <FiSave className="mr-2" />
                {isSubmitting ? 'Saving...' : 'Save Textures'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </main>
    </div>
  )
}