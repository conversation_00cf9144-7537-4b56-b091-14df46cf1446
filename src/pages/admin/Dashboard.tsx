import { useQuery } from '@tanstack/react-query'
import { apiService } from '@/services/api'
import { FiUsers, FiPackage, FiDollarSign, FiBarChart2, FiAlertCircle } from 'react-icons/fi'

type DashboardStats = {
  totalUsers: number
  totalOrders: number
  totalRevenue: number
  pendingOrders: number
  completedOrders: number
  monthlyRevenue: number[]
  monthlySales: number[]
}

const AdminDashboard = () => {
  // Fetch dashboard statistics
  const { data: stats, isLoading, isError } = useQuery<DashboardStats>({
    queryKey: ['dashboardStats'],
    queryFn: async () => {
      return apiService.get<DashboardStats>('/orders/dashboard-stats')
    }
  })

  // Format currency
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (isError || !stats) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex flex-col justify-center items-center h-64 text-red-500">
          <div className="mb-4"><FiAlertCircle size={48} /></div>
          <p>There was an error loading the dashboard data. Please try again later.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center">
          <div className="rounded-full bg-blue-100 p-3 mr-4">
            <div className="text-blue-600">
              <FiUsers size={24} />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Users</p>
            <p className="text-2xl font-bold">{stats.totalUsers}</p>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center">
          <div className="rounded-full bg-green-100 p-3 mr-4">
            <div className="text-green-600">
              <FiDollarSign size={24} />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Revenue</p>
            <p className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</p>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center">
          <div className="rounded-full bg-purple-100 p-3 mr-4">
            <div className="text-purple-600">
              <FiPackage size={24} />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Total Orders</p>
            <p className="text-2xl font-bold">{stats.totalOrders}</p>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border flex items-center">
          <div className="rounded-full bg-yellow-100 p-3 mr-4">
            <div className="text-yellow-600">
              <FiBarChart2 size={24} />
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Pending Orders</p>
            <p className="text-2xl font-bold">{stats.pendingOrders}</p>
          </div>
        </div>
      </div>
      
      {/* Order Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
          <div className="space-y-2">
            <div className="flex justify-between py-2 border-b">
              <span>Pending Orders</span>
              <span className="font-semibold">{stats.pendingOrders}</span>
            </div>
            <div className="flex justify-between py-2 border-b">
              <span>Completed Orders</span>
              <span className="font-semibold">{stats.completedOrders}</span>
            </div>
            <div className="flex justify-between py-2 border-b">
              <span>Total Orders</span>
              <span className="font-semibold">{stats.totalOrders}</span>
            </div>
            <div className="flex justify-between py-2">
              <span>Completion Rate</span>
              <span className="font-semibold">
                {stats.totalOrders > 0 ? Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0}%
              </span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-xl font-semibold mb-4">Revenue Summary</h2>
          <div className="space-y-2">
            <div className="flex justify-between py-2 border-b">
              <span>Total Revenue</span>
              <span className="font-semibold">{formatCurrency(stats.totalRevenue)}</span>
            </div>
            <div className="flex justify-between py-2 border-b">
              <span>Average Order Value</span>
              <span className="font-semibold">
                {formatCurrency(stats.totalOrders > 0 ? stats.totalRevenue / stats.totalOrders : 0)}
              </span>
            </div>
            <div className="flex justify-between py-2">
              <span>Monthly Average</span>
              <span className="font-semibold">
                {formatCurrency(stats.monthlyRevenue.length > 0 ? 
                  stats.monthlyRevenue.reduce((sum, value) => sum + value, 0) / stats.monthlyRevenue.length : 0)}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Monthly Chart Placeholder (would typically use a chart library like Chart.js or Recharts) */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-xl font-semibold mb-4">Monthly Performance</h2>
        <div className="flex items-center justify-center h-64 border rounded bg-gray-50">
          <p className="text-gray-500">Monthly performance chart would be displayed here</p>
          {/* In a real implementation, you would render a chart here using the monthlyRevenue and monthlySales data */}
        </div>
      </div>
    </div>
  )
}

export default AdminDashboard