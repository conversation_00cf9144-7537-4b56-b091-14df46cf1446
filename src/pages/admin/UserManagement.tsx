import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiService } from '@/services/api'
import { FiPlus, FiEdit2, FiTrash2, FiAlertCircle, FiSearch, FiEye, FiMail, FiMapPin, FiPhone, FiUser, FiX, FiHome, FiGrid, FiList, FiSave, FiCheck, FiLock, FiUserPlus, FiSettings, FiSquare } from 'react-icons/fi'
import { toast } from 'react-toastify'
import * as Dialog from '@radix-ui/react-dialog'
import { User, StoreSetting } from '@/types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { api } from '@/services/api'
import { useTranslation } from 'react-i18next'


const userSchema = (t: (key: string) => string) => z.object({
  username: z.string().email(t('invalidEmail')).min(3, t('usernameMinLength')),
  password: z.string().min(8, t('passwordMinLength')),
  storeName: z.string().min(1, t('storeNameRequired')),
})

type UserFormData = z.infer<ReturnType<typeof userSchema>>

const UserManagement = () => {
  const queryClient = useQueryClient()
  const { t } = useTranslation('userCreation')
  const { t: tUser } = useTranslation('userManagement')
  const [searchTerm, setSearchTerm] = useState('')
  const [isDeleting, setIsDeleting] = useState<string | null>(null)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')
  const [editingUser, setEditingUser] = useState<User | null>(null)

  const { register, handleSubmit, reset, setValue } = useForm<User>()
  const { register: registerCreate, handleSubmit: handleSubmitCreate, reset: resetCreate, formState: { errors: createErrors } } = useForm<UserFormData>({
    resolver: zodResolver(userSchema(t)),
  })

  // Fetch users data
  const { data: users, isLoading, isError } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: async () => {
      return apiService.get<User[]>('/user')
    }
  })

  // Fetch store settings for selected user
  const { data: storeSettings, isLoading: isLoadingSettings, isError: isErrorSettings } = useQuery<StoreSetting | null>({
    queryKey: ['storeSettings', selectedUser?.id],
    queryFn: async () => {
      if (!selectedUser?.id) throw new Error('No user selected')
      try {
        return await apiService.get<StoreSetting>(`/StoreSettings/user/${selectedUser.id}`)
      } catch (error: any) {
        return null
      }
    },
    enabled: !!selectedUser?.id && isModalOpen,
    retry: false, // Disable retries
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  })

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: async (userId: string) => {
      return apiService.delete(`/User/${userId}`)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success(tUser('userDeletedSuccessfully'))
      setIsDeleting(null)
    },
    onError: (error) => {
      console.error('Error deleting user:', error)
      toast.error(tUser('failedToDeleteUser'))
      setIsDeleting(null)
    }
  })

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async (data: User) => {
      return apiService.put(`/User/${data.id}`, data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success(tUser('userUpdatedSuccessfully'))
      setIsEditModalOpen(false)
      setEditingUser(null)
      reset()
    },
    onError: (error) => {
      console.error('Error updating user:', error)
      toast.error(tUser('failedToUpdateUser'))
    }
  })

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (data: UserFormData) => {
      return api.post('/User', data)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] })
      toast.success(t('userCreatedSuccessfully'))
      setIsCreateModalOpen(false)
      resetCreate()
    },
    onError: (error) => {
      console.error('Error creating user:', error)
      toast.error(t('failedToCreateUser'))
    }
  })

  const handleDeleteUser = (userId: string) => {
    if (window.confirm(tUser('confirmDeleteMessage'))) {
      setIsDeleting(userId)
      deleteUserMutation.mutate(userId)
    }
  }

  const handleViewDetails = (user: User) => {
    setSelectedUser(user)
    setIsModalOpen(true)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setIsEditModalOpen(true)

    // Populate form with user data
    setValue('username', user.username)
    setValue('email', user.email || '')
    setValue('storeName', user.storeName || '')
    setValue('role', user.role)
    setValue('isActive', user.isActive)
  }

  const onSubmit = (data: User) => {
    if (editingUser) {
      updateUserMutation.mutate({ ...data, id: editingUser.id })
    }
  }

  const onSubmitCreate = (data: UserFormData) => {
    createUserMutation.mutate(data)
  }

  const filteredUsers = users
    ? users.filter(user =>
      // Only include active users
      user.isActive && (
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.storeName && user.storeName.toLowerCase().includes(searchTerm.toLowerCase())) ||
        user.role.toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
    : []

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="flex justify-center items-center h-64">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-200"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-8">
            <div className="flex flex-col justify-center items-center h-64 text-red-500">
              <div className="mb-6 p-4 bg-red-100 rounded-full">
                <FiAlertCircle size={48} />
              </div>
              <h3 className="text-xl font-semibold mb-2">{tUser('errorLoadingUsers')}</h3>
              <p className="text-gray-600">{tUser('errorLoadingUsersMessage')}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">

        {/* Header Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                {tUser('storeManagement')}
              </h1>
              <p className="text-gray-600 mt-2">{tUser('manageStoreAccounts')}</p>
            </div>
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="group relative inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <FiPlus className="w-5 h-5 transition-transform group-hover:rotate-90" />
              <span>{tUser('addNewStore')}</span>
              <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>

        {/* Search and Stats Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Search Bar */}
          <div className="lg:col-span-2">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6">
              <div className="relative">
                <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  className="w-full pl-12 pr-4 py-4 bg-gray-50/50 border border-gray-200 rounded-xl text-lg placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                  placeholder={tUser('searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Stats Card */}
          <div className="bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl shadow-xl p-6 text-white">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <FiHome className="w-8 h-8" />
              </div>
              <div>
                <p className="text-purple-100 text-sm">{tUser('totalStores')}</p>
                <p className="text-3xl font-bold">{filteredUsers.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Users Grid */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
          <div className="p-6 border-b border-gray-200/50">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-800">{tUser('storeAccounts')}</h2>
                <p className="text-gray-600 mt-1">{tUser('clickToViewDetails')}</p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-3 rounded-lg transition-all duration-300 ${viewMode === 'grid'
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  title="Grid View"
                >
                  <FiGrid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`p-3 rounded-lg transition-all duration-300 ${viewMode === 'table'
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  title="Table View"
                >
                  <FiList className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {filteredUsers.length > 0 ? (
            viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {filteredUsers.map((user) => (
                  <div
                    key={user.id}
                    className="group bg-white rounded-xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden transition-all duration-300 transform hover:-translate-y-1 cursor-pointer"
                    onClick={() => handleViewDetails(user)}
                  >
                    {/* Card Header */}
                    <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-100">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl text-white">
                          <FiHome className="w-6 h-6" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-gray-900 truncate group-hover:text-purple-600 transition-colors">
                            {user.storeName || 'Unnamed Store'}
                          </h3>
                          <p className="text-sm text-gray-600 flex items-center gap-1">
                            <FiUser className="w-4 h-4" />
                            {user.username}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Card Body */}
                    <div className="p-6">
                      <div className="space-y-3">

                        {user.email && (
                          <div className="flex items-center gap-3 text-sm text-gray-600">
                            <FiMail className="w-4 h-4 text-blue-500" />
                            <span>{user.email}</span>
                          </div>
                        )}
                      </div>

                      {/* Status and Role */}
                      <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${user.role === 'admin'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-blue-100 text-blue-800'
                          }`}>
                          {user.role}
                        </span>

                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${user.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                          }`}>
                          {user.isActive ? tUser('active') : tUser('inactive')}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="bg-gray-50 px-6 py-4 flex items-center gap-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleViewDetails(user)
                        }}
                        className="flex-1 inline-flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        <FiEye className="w-4 h-4" />
                        {tUser('viewDetails')}
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEditUser(user)
                        }}
                        className="p-2 text-gray-600 hover:text-purple-600 hover:bg-white rounded-lg transition-colors"
                        title="Edit Store"
                      >
                        <FiEdit2 className="w-4 h-4" />
                      </button>

                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteUser(user.id)
                        }}
                        disabled={isDeleting === user.id}
                        className={`p-2 text-gray-600 hover:text-red-600 hover:bg-white rounded-lg transition-colors ${isDeleting === user.id ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                        title="Delete Store"
                      >
                        {isDeleting === user.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-600 border-t-transparent"></div>
                        ) : (
                          <FiTrash2 className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Table View
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200/50 bg-gray-50/50">
                      <th className="text-left p-4 text-sm font-semibold text-gray-700">{tUser('storeName')}</th>
                      <th className="text-left p-4 text-sm font-semibold text-gray-700">{tUser('username')}</th>
                      <th className="text-left p-4 text-sm font-semibold text-gray-700">{tUser('role')}</th>
                      <th className="text-left p-4 text-sm font-semibold text-gray-700">{tUser('status')}</th>
                      <th className="text-right p-4 text-sm font-semibold text-gray-700">{tUser('actions')}</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50/50 transition-colors">
                        <td className="p-4">
                          <div className="font-medium text-gray-900">
                            {user.storeName || 'Unnamed Store'}
                          </div>
                        </td>
                        <td className="p-4 text-sm text-gray-600">{user.username}</td>
                        <td className="p-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${user.role === 'admin'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-blue-100 text-blue-800'
                            }`}>
                            {user.role}
                          </span>
                        </td>
                        <td className="p-4">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${user.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {user.isActive ? tUser('active') : tUser('inactive')}
                          </span>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center justify-end gap-2">
                            <button
                              onClick={() => handleViewDetails(user)}
                              className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
                              title="View Details"
                            >
                              <FiEye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleEditUser(user)}
                              className="p-2 text-purple-600 hover:text-purple-800 hover:bg-purple-50 rounded-lg transition-colors"
                              title="Edit Store"
                            >
                              <FiEdit2 className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              disabled={isDeleting === user.id}
                              className={`p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors ${isDeleting === user.id ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                              title="Delete Store"
                            >
                              {isDeleting === user.id ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-2 border-red-600 border-t-transparent"></div>
                              ) : (
                                <FiTrash2 className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )
          ) : (
            <div className="p-12 text-center">
              <div className="p-6 bg-gray-50 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                <FiHome className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{tUser('noStoresFound')}</h3>
              <p className="text-gray-600 mb-6">
                {searchTerm ? tUser('noStoresMatchSearch') : tUser('noStoresCreated')}
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  <FiPlus className="w-4 h-4" />
                  {tUser('createFirstStore')}
                </button>
              )}
            </div>
          )}
        </div>

        {/* Enhanced Modal */}
        <Dialog.Root open={isModalOpen} onOpenChange={setIsModalOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 animate-in fade-in duration-300" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden z-50 animate-in zoom-in-95 duration-300">
              {selectedUser && (
                <>
                  {/* Modal Header */}
                  <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-white/20 rounded-xl">
                          <FiHome className="w-8 h-8" />
                        </div>
                        <div>
                          <Dialog.Title className="text-2xl font-bold">
                            {selectedUser.storeName || 'Store Details'}
                          </Dialog.Title>
                          <p className="text-purple-100 mt-1">Complete store information</p>
                        </div>
                      </div>
                      <Dialog.Close className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <FiX className="w-6 h-6" />
                      </Dialog.Close>
                    </div>
                  </div>

                  {/* Modal Body */}
                  <div className="p-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                      {/* Basic Information */}
                      <div className="space-y-6">
                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                          {tUser('basicInformation')}
                        </h3>

                        <div className="space-y-4">
                          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
                            <FiUser className="w-5 h-5 text-purple-500" />
                            <div>
                              <p className="text-sm font-medium text-gray-700">{tUser('username')}</p>
                              <p className="text-gray-900">{selectedUser.username}</p>
                            </div>
                          </div>


                          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
                            <FiMail className="w-5 h-5 text-blue-500" />
                            <div>
                              <p className="text-sm font-medium text-gray-700">{tUser('email')}</p>
                              <p className="text-gray-900">{selectedUser.email || tUser('notProvided')}</p>
                            </div>
                          </div>
                        </div>
                      </div>


                    </div>

                    {/* Status Information */}
                    <div className="mt-8 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">{tUser('accountStatus')}</h3>
                      <div className="flex items-center gap-6">
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-700">{tUser('role')}:</span>
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${selectedUser.role === 'admin'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-blue-100 text-blue-800'
                            }`}>
                            {selectedUser.role}
                          </span>
                        </div>

                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-700">{tUser('status')}:</span>
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${selectedUser.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                            }`}>
                            {selectedUser.isActive ? tUser('active') : tUser('inactive')}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Store Settings Information */}
                    <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                      <div className="flex items-center gap-3 mb-6">
                        <div className="p-2 bg-blue-500 text-white rounded-lg">
                          <FiSettings className="w-6 h-6" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{tUser('storeSettings')}</h3>
                          <p className="text-blue-600 text-sm">{tUser('storeSettingsDetails')}</p>
                        </div>
                      </div>

                      {isLoadingSettings ? (
                        <div className="flex items-center justify-center py-8">
                          <div className="relative">
                            <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-200"></div>
                            <div className="animate-spin rounded-full h-8 w-8 border-4 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
                          </div>
                          <span className="ml-3 text-blue-600">{tUser('loadingStoreSettings')}</span>
                        </div>
                      ) : isErrorSettings ? (
                        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                          <div className="flex items-center gap-3 text-red-600">
                            <FiAlertCircle className="w-5 h-5" />
                            <span className="text-sm font-medium">{tUser('failedToLoadStoreSettings')}</span>
                          </div>
                        </div>
                      ) : storeSettings ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Company Information */}
                          <div className="space-y-3">
                            <h4 className="font-semibold text-gray-900 border-b border-blue-200 pb-2 flex items-center gap-2">
                              <FiSquare className="w-4 h-4 text-blue-500" />
                              {tUser('companyName')}
                            </h4>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('companyName')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.companyName || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('taxNumber')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.taxNumber || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('profitMargin')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.profitMargin}%</span>
                              </div>
                            </div>
                          </div>

                          {/* Contact Information */}
                          <div className="space-y-3">
                            <h4 className="font-semibold text-gray-900 border-b border-blue-200 pb-2 flex items-center gap-2">
                              <FiPhone className="w-4 h-4 text-blue-500" />
                              Contact & Management
                            </h4>
                            <div className="space-y-3">
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('ownerName')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.ownerName || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('ownerPhone')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.ownerPhone || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('managerName')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.managerName || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[100px]">{tUser('managerPhone')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.managerPhone || tUser('notProvided')}</span>
                              </div>
                            </div>
                          </div>

                          {/* Store & Shipping Information */}
                          <div className="md:col-span-2 space-y-3">
                            <h4 className="font-semibold text-gray-900 border-b border-blue-200 pb-2 flex items-center gap-2">
                              <FiMapPin className="w-4 h-4 text-blue-500" />
                              Store & Shipping
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[120px]">{tUser('storeAddress')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.storeAddress || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[120px]">{tUser('storePhone')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.storePhone || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[120px]">{tUser('shippingCompany')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.shippingCompany || tUser('notProvided')}</span>
                              </div>
                              <div className="flex justify-between items-center gap-3 p-3 bg-white/70 rounded-lg">
                                <span className="text-sm font-medium text-gray-700 min-w-[120px]">{tUser('shippingPhone')}:</span>
                                <span className="text-gray-900 font-semibold">{storeSettings.shippingPhone || tUser('notProvided')}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                          <div className="flex items-center gap-3 text-gray-600">
                            <FiAlertCircle className="w-5 h-5" />
                            <span className="text-sm">{tUser('noStoreSettings')}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>

        {/* Edit User Modal */}
        <Dialog.Root open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 animate-in fade-in duration-300" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden z-50 animate-in zoom-in-95 duration-300">
              {editingUser && (
                <>
                  {/* Modal Header */}
                  <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-3 bg-white/20 rounded-xl">
                          <FiEdit2 className="w-8 h-8" />
                        </div>
                        <div>
                          <Dialog.Title className="text-2xl font-bold">
                            {tUser('editStoreInformation')}
                          </Dialog.Title>
                          <p className="text-purple-100 mt-1">{tUser('updateStoreDetails')}</p>
                        </div>
                      </div>
                      <Dialog.Close className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                        <FiX className="w-6 h-6" />
                      </Dialog.Close>
                    </div>
                  </div>

                  {/* Modal Body */}
                  <form onSubmit={handleSubmit(onSubmit)} className="p-8 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                      {/* Basic Information */}
                      <div className="space-y-6">
                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                          {tUser('basicInformation')}
                        </h3>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {tUser('username')}
                          </label>
                          <input
                            type="text"
                            {...register('username')}
                            className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {tUser('email')}
                          </label>
                          <input
                            type="email"
                            {...register('email')}
                            className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {tUser('storeName')}
                          </label>
                          <input
                            type="text"
                            {...register('storeName')}
                            className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          />
                        </div>
                      </div>

                      {/* Account Settings */}
                      <div className="space-y-6">
                        <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                          {tUser('accountSettings')}
                        </h3>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {tUser('role')}
                          </label>
                          <select
                            {...register('role')}
                            className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          >
                            <option value="store">Store</option>
                            <option value="admin">Admin</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {tUser('status')}
                          </label>
                          <div className="flex items-center h-[50px]">
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                {...register('isActive')}
                                className="sr-only peer"
                              />
                              <div className="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-purple-300 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                              <span className="ml-3 text-sm font-medium text-gray-700">{tUser('active')}</span>
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end gap-4">
                      <Dialog.Close
                        type="button"
                        className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-colors"
                      >
                        {tUser('cancel')}
                      </Dialog.Close>
                      <button
                        type="submit"
                        disabled={updateUserMutation.isPending}
                        className="group relative inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {updateUserMutation.isPending ? (
                          <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                        ) : (
                          <FiSave className="w-5 h-5" />
                        )}
                        <span>{updateUserMutation.isPending ? tUser('saving') : tUser('saveChanges')}</span>
                      </button>
                    </div>
                  </form>
                </>
              )}
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>

        {/* Create User Modal */}
        <Dialog.Root open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 animate-in fade-in duration-300" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden z-50 animate-in zoom-in-95 duration-300">
              {/* Modal Header */}
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="p-3 bg-white/20 rounded-xl">
                      <FiUserPlus className="w-8 h-8" />
                    </div>
                    <div>
                      <Dialog.Title className="text-2xl font-bold">
                        {t('createNewStoreUser')}
                      </Dialog.Title>
                      <p className="text-purple-100 mt-1">Fill in the details to create a new store account</p>
                    </div>
                  </div>
                  <Dialog.Close className="p-2 hover:bg-white/20 rounded-lg transition-colors">
                    <FiX className="w-6 h-6" />
                  </Dialog.Close>
                </div>
              </div>

              {/* Modal Body */}
              <form onSubmit={handleSubmitCreate(onSubmitCreate)} className="p-8">
                <div className="space-y-8">

                  {/* Store Name */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <FiHome className="w-5 h-5 text-purple-500" />
                      {t('storeName')}
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        {...registerCreate('storeName')}
                        className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${createErrors.storeName ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                          }`}
                        placeholder={t('storeNamePlaceholder')}
                      />
                      <FiHome className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    </div>
                    {createErrors.storeName && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600 flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                            <span className="text-white text-xs">!</span>
                          </div>
                          {createErrors.storeName.message}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Username (Email) */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <FiMail className="w-5 h-5 text-blue-500" />
                      {t('usernameEmail')}
                    </label>
                    <div className="relative">
                      <input
                        type="email"
                        {...registerCreate('username')}
                        className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${createErrors.username ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                          }`}
                        placeholder={t('emailPlaceholder')}
                      />
                      <FiMail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    </div>
                    {createErrors.username && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600 flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                            <span className="text-white text-xs">!</span>
                          </div>
                          {createErrors.username.message}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Password */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <FiLock className="w-5 h-5 text-green-500" />
                      {t('password')}
                    </label>
                    <div className="relative">
                      <input
                        type="password"
                        {...registerCreate('password')}
                        className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${createErrors.password ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                          }`}
                        placeholder={t('passwordPlaceholder')}
                      />
                      <FiLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    </div>
                    {createErrors.password && (
                      <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600 flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                            <span className="text-white text-xs">!</span>
                          </div>
                          {createErrors.password.message}
                        </p>
                      </div>
                    )}
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-600 flex items-center gap-2">
                        <FiCheck className="w-4 h-4" />
                        Password must be at least 8 characters long
                      </p>
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end gap-4">
                  <Dialog.Close
                    type="button"
                    className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl font-medium transition-colors"
                  >
                    Cancel
                  </Dialog.Close>
                  <button
                    type="submit"
                    disabled={createUserMutation.isPending}
                    className="group relative inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {createUserMutation.isPending ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                        <span>{t('creating')}</span>
                      </>
                    ) : (
                      <>
                        <FiUserPlus className="w-5 h-5 transition-transform group-hover:scale-110" />
                        <span>{t('createUser')}</span>
                        <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </div>
  )
}

export default UserManagement