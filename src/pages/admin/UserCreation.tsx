import { useState } from 'react'
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiUserPlus, FiHome, FiMail, FiLock, FiCheck } from 'react-icons/fi'
import { api } from '@/services/api'
import { useTranslation } from 'react-i18next'
import { toast } from 'react-toastify'

const userSchema = (t: (key: string) => string) => z.object({
  username: z.string().email(t('invalidEmail')).min(3, t('usernameMinLength')),
  password: z.string().min(8, t('passwordMinLength')),
  storeName: z.string().min(1, t('storeNameRequired')),
})

type UserFormData = z.infer<ReturnType<typeof userSchema>>

export default function UserCreation() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t } = useTranslation('userCreation')

  const { register, handleSubmit, reset, formState: { errors } } = useForm<UserFormData>({
    resolver: zodResolver(userSchema(t)),
  })

  const onSubmit: SubmitHandler<UserFormData> = async (data) => {
    setIsSubmitting(true)
    try {
      await api.post('/User', data)
      toast.success(t('userCreatedSuccessfully'))
      reset()
    } catch (error) {
      toast.error(t('failedToCreateUser'))
      console.error(t('errorCreatingUser'), error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        
        {/* Header Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                {t('userCreation')}
              </h1>
              <p className="text-gray-600 mt-2">Create new store accounts with secure credentials</p>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg text-sm font-medium">
              <FiUserPlus className="w-4 h-4" />
              New Store Setup
            </div>
          </div>
        </div>

        {/* User Creation Form */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden max-w-2xl mx-auto">
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-8">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl">
                <FiUserPlus className="w-8 h-8" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">{t('createNewStoreUser')}</h2>
                <p className="text-purple-100 mt-1">Fill in the details to create a new store account</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-8">
            <div className="space-y-8">
              
              {/* Store Name */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <FiHome className="w-5 h-5 text-purple-500" />
                  {t('storeName')}
                </label>
                <div className="relative">
                  <input
                    type="text"
                    {...register('storeName')}
                    className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${
                      errors.storeName ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                    }`}
                    placeholder={t('storeNamePlaceholder')}
                  />
                  <FiHome className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                </div>
                {errors.storeName && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600 flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-white text-xs">!</span>
                      </div>
                      {errors.storeName.message}
                    </p>
                  </div>
                )}
              </div>

              {/* Username (Email) */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <FiMail className="w-5 h-5 text-blue-500" />
                  {t('usernameEmail')}
                </label>
                <div className="relative">
                  <input
                    type="email"
                    {...register('username')}
                    className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${
                      errors.username ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                    }`}
                    placeholder={t('emailPlaceholder')}
                  />
                  <FiMail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                </div>
                {errors.username && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600 flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-white text-xs">!</span>
                      </div>
                      {errors.username.message}
                    </p>
                  </div>
                )}
              </div>

              {/* Password */}
              <div className="group">
                <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                  <FiLock className="w-5 h-5 text-green-500" />
                  {t('password')}
                </label>
                <div className="relative">
                  <input
                    type="password"
                    {...register('password')}
                    className={`w-full px-4 py-4 pl-12 bg-gray-50/50 border rounded-xl text-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent group-hover:bg-white ${
                      errors.password ? 'border-red-300 bg-red-50/30' : 'border-gray-200'
                    }`}
                    placeholder={t('passwordPlaceholder')}
                  />
                  <FiLock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                </div>
                {errors.password && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-600 flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-red-500 flex items-center justify-center">
                        <span className="text-white text-xs">!</span>
                      </div>
                      {errors.password.message}
                    </p>
                  </div>
                )}
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-600 flex items-center gap-2">
                    <FiCheck className="w-4 h-4" />
                    Password must be at least 8 characters long
                  </p>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative w-full inline-flex items-center justify-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                    <span>{t('creating')}</span>
                  </>
                ) : (
                  <>
                    <FiUserPlus className="w-5 h-5 transition-transform group-hover:scale-110" />
                    <span>{t('createUser')}</span>
                    <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}