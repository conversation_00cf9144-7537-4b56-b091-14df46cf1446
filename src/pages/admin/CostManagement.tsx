import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { apiGet, apiPut } from '@/services/api'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { FiSave, FiDollarSign, FiSettings, FiPackage, FiLayers, FiGrid } from 'react-icons/fi'

interface PricingDataDto {
  generalOutcomeArmchair: number
  generalOutcomeBergere: number
  profitRate: number
  fixedFabricAmount: number
  lowerFrameFixedPrice: number
  fabrics: Record<string, Record<string, number>>
  seatOptions: Record<string, { price: number; fabricAmount: number }>
  armrestBase: Record<string, { price: number; fabricAmount: number }>
  lowerFrame: Record<string, number>
  legOptions: Record<string, Record<string, number>>
  backPillows: Record<string, { price: number; fabricAmount: number }>
  cushions: Record<string, { price: number; fabricAmount: number }>
  bergereOptions: Record<string, { price: number; fabricAmount: number }>
}

export default function CostManagement() {
  const { t } = useTranslation('costManagement')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const { register, handleSubmit, setValue } = useForm<PricingDataDto>()

  // Define all the options
  const seatOptions = ['1', '3', '4'] // çektirme, tek parça, bomba
  const armrestOptions = ['1', '2', '3', '4', '5']
  const lowerFrameColors = ['Ceviz', 'Beyaz', 'Ekru', 'Gri', 'Siyah', 'Sarı Eskitme', 'Siyah Eskitme', 'Ceviz Eskitme']
  const woodenLegColors = ['Ceviz', 'Beyaz', 'Ekru', 'Gri', 'Antrasit', 'Sarı Eskitme', 'Gri Eskitme', 'Ceviz Eskitme']
  const metalLegColors = ['Bronz', 'Gold', 'Nikel']
  const legTypes = ['1', '2', '6', '7', '8', '9', '10', '11', '12']
  const cushionTypes = ['Sünger', 'Silikon Elyaf', 'Kırlent 1', 'Kırlent 2', 'Kırlent 3']
  const bergereTypes = ['Bergere 1', 'Bergere 2', 'Bergere 3']

  useEffect(() => {
    fetchPricingData()
  }, [])

  const fetchPricingData = async () => {
    try {
      setLoading(true)
      const response = await apiGet<PricingDataDto>('/furniture/pricing')
      const data = response.data

      // Ensure fabrics are properly formatted as objects
      if (data.fabrics) {
        Object.entries(data.fabrics).forEach(([kartela, colors]) => {
          if (Array.isArray(colors)) {
            // Convert array to object
            const colorObject: Record<string, number> = {}
            colors.forEach((value, index) => {
              if (value !== null && value !== undefined) {
                colorObject[String(index + 1)] = value
              }
            })
            data.fabrics[kartela] = colorObject
          }
        })
      }

      // Set form values
      Object.entries(data).forEach(([key, value]) => {
        setValue(key as keyof PricingDataDto, value)
      })
    } catch (error) {
      console.error('Failed to fetch pricing data:', error)
      toast.error(t('failedToLoadPricing'))
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: PricingDataDto) => {
    try {
      setSaving(true)

      // Ensure all fabric kartelas are properly formatted as objects
      const transformedData = {
        ...data,
        fabrics: Object.fromEntries(
          Object.entries(data.fabrics || {}).map(([kartela, colors]) => {
            // If colors is an array, convert it to an object
            if (Array.isArray(colors)) {
              const colorObject: Record<string, number> = {}
              colors.forEach((value, index) => {
                if (value !== null && value !== undefined) {
                  colorObject[String(index + 1)] = value
                }
              })
              return [kartela, colorObject]
            }
            return [kartela, colors]
          })
        )
      }

      await apiPut('/furniture/pricing', transformedData)
      toast.success(t('pricingUpdatedSuccessfully'))
    } catch (error) {
      console.error('Failed to update pricing:', error)
      toast.error(t('failedToUpdatePricing'))
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="flex justify-center items-center h-64">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-200"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">

        {/* Header Section */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-gray-600 mt-2">{t('description')}</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg text-sm font-medium">
                {t('livePricing')}
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* General Settings */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiSettings className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('generalSettings')}</h2>
                  <p className="text-gray-600 text-sm">{t('baseConfigurationAndProfitMargins')}</p>
                </div>
              </div>
            </div>
            <div className="p-6">

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="group">
                  <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <FiPackage className="w-4 h-4 text-purple-500" />
                    {t('generalOutcomeArmchair')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('generalOutcomeArmchair', { valueAsNumber: true })}
                    className="w-full px-4 py-3 bg-gray-50/50 border border-gray-200 rounded-xl text-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-white"
                  />
                </div>

                <div className="group">
                  <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <FiPackage className="w-4 h-4 text-blue-500" />
                    {t('generalOutcomeBergere')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('generalOutcomeBergere', { valueAsNumber: true })}
                    className="w-full px-4 py-3 bg-gray-50/50 border border-gray-200 rounded-xl text-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-white"
                  />
                </div>

                <div className="group">
                  <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <FiDollarSign className="w-4 h-4 text-green-500" />
                    {t('profitRate')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('profitRate', { valueAsNumber: true })}
                    className="w-full px-4 py-3 bg-gray-50/50 border border-gray-200 rounded-xl text-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-white"
                  />
                </div>

                <div className="group">
                  <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <FiLayers className="w-4 h-4 text-orange-500" />
                    {t('fixedFabricAmount')}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    {...register('fixedFabricAmount', { valueAsNumber: true })}
                    className="w-full px-4 py-3 bg-gray-50/50 border border-gray-200 rounded-xl text-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-white"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Fabric Prices - All 12 Kartelas */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiGrid className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('fabricPrices')}</h2>
                  <p className="text-gray-600 text-sm">{t('configurePricingForAllFabricKartelasAndColors')}</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map((kartela) => {
                  // Determine number of colors for each cartela
                  const colorCount = kartela === 12 ? 6 : kartela === 8 ? 10 : 9;

                  return (
                    <div key={`kartela${kartela}`} className="group bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-gradient-to-r from-purple-400 to-blue-400"></div>
                        Kartela {kartela}
                      </h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {Array.from({ length: colorCount }, (_, i) => i + 1).map((color) => (
                          <div key={`kartela${kartela}-${color}`} className="space-y-2">
                            <label className="block text-xs font-medium text-gray-600">
                              {t('color')} {color}
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              {...register(`fabrics.Kartela ${kartela}.${color}`, { valueAsNumber: true })}
                              className="w-full px-3 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Seat Options */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiPackage className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('seatOptions')}</h2>
                  <p className="text-gray-600 text-sm">Configure seat options and fabric requirements</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {seatOptions.map((option) => (
                  <div key={`seat-${option}`} className="group bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-lg flex items-center justify-center font-bold">
                        {option}
                      </div>
                      {t(`seatOption${option}`)}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('price')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`seatOptions.OturumSeçenek ${option}.price`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('fabricMeterage')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`seatOptions.OturumSeçenek ${option}.fabricAmount`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Armrest Options */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiLayers className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('armrestOptions')}</h2>
                  <p className="text-gray-600 text-sm">Configure armrest options and pricing</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {armrestOptions.map((option) => (
                  <div key={`armrest-${option}`} className="group bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg flex items-center justify-center font-bold">
                        {option}
                      </div>
                      {t(`armOption`)} {option}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('price')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`armrestBase.${option}.price`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('fabricMeterage')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`armrestBase.${option}.fabricAmount`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Lower Frame (Alt Kasa) */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiGrid className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('lowerFrame')}</h2>
                  <p className="text-gray-600 text-sm">{t('configureLowerFrameColorPricing')}</p>
                </div>
              </div>
            </div>
            <div className="p-6 flex flex-col gap-20">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    <FiGrid className="w-4 h-4 text-red-500" />
                    {t('lowerFrameFixedPrice')}
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₺</span>
                    <input
                      type="number"
                      step="0.01"
                      {...register('lowerFrameFixedPrice', { valueAsNumber: true })}
                      className="w-full pl-8 pr-3 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-gray-50"
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
                {lowerFrameColors.map((color) => (
                  <div key={`lowerFrame-${color}`} className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      {t(color)}
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₺</span>
                      <input
                        type="number"
                        step="0.01"
                        {...register(`lowerFrame.${color}`, { valueAsNumber: true })}
                        className="w-full pl-8 pr-3 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-gray-50"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Leg Options */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiLayers className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('legOptions')}</h2>
                  <p className="text-gray-600 text-sm">Configure leg type and color pricing</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {legTypes.map((legType) => (
                  <div key={`leg-${legType}`} className="bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg flex items-center justify-center font-bold">
                        {legType}
                      </div>
                      {t('legType')} {legType}
                    </h3>

                    {/* Wooden Colors */}
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-amber-400 to-amber-600 rounded"></div>
                        {t('woodenLegColors')}
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {woodenLegColors.map((color) => (
                          <div key={`leg-${legType}-wooden-${color}`} className="group">
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              {t(color)}
                            </label>
                            <div className="relative">
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs">₺</span>
                              <input
                                type="number"
                                step="0.01"
                                {...register(`legOptions.${legType}.${color}`, { valueAsNumber: true })}
                                className="w-full pl-6 pr-2 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-amber-50/30"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Metal Colors */}
                    <div>
                      <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-gray-400 to-gray-600 rounded"></div>
                        {t('metalLegColors')}
                      </h4>
                      <div className="grid grid-cols-3 gap-3">
                        {metalLegColors.map((color) => (
                          <div key={`leg-${legType}-metal-${color}`} className="group">
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              {t(color)}
                            </label>
                            <div className="relative">
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 text-xs">₺</span>
                              <input
                                type="number"
                                step="0.01"
                                {...register(`legOptions.${legType}.${color}`, { valueAsNumber: true })}
                                className="w-full pl-6 pr-2 py-2 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300 group-hover:bg-gray-50"
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Back Pillows & Cushions */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiPackage className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('backPillowsAndCushions')}</h2>
                  <p className="text-gray-600 text-sm">Configure cushion types and fabric requirements</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {cushionTypes.map((type) => (
                  <div key={`cushion-${type}`} className="group bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-2 h-2 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full"></div>
                      {t(type)}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('price')}
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₺</span>
                          <input
                            type="number"
                            step="0.01"
                            {...register(`cushions.${type}.price`, { valueAsNumber: true })}
                            className="w-full pl-8 pr-3 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('fabricMeterage')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`cushions.${type}.fabricAmount`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Bergere Options */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 overflow-hidden">
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg text-white">
                  <FiLayers className="w-6 h-6" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-800">{t('bergereOptions')}</h2>
                  <p className="text-gray-600 text-sm">Configure bergere models and pricing</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {bergereTypes.map((type) => (
                  <div key={`bergere-${type}`} className="group bg-gradient-to-br from-gray-50 to-white border border-gray-200/50 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-lg flex items-center justify-center font-bold">
                        {type.split(' ')[1]}
                      </div>
                      {t(type)}
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('price')}
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₺</span>
                          <input
                            type="number"
                            step="0.01"
                            {...register(`bergereOptions.${type}.price`, { valueAsNumber: true })}
                            className="w-full pl-8 pr-3 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-2">
                          {t('fabricMeterage')}
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          {...register(`bergereOptions.${type}.fabricAmount`, { valueAsNumber: true })}
                          className="w-full px-4 py-3 bg-white border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/50 p-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{t('readyToSaveChanges')}</h3>
                <p className="text-sm text-gray-600">{t('allPricingConfigurationsWillBeUpdated')}</p>
              </div>
              <button
                type="submit"
                disabled={saving}
                className="group relative inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                ) : (
                  <FiSave className="w-5 h-5 transition-transform group-hover:scale-110" />
                )}
                <span>{saving ? t('saving') : t('saveChanges')}</span>
                {!saving && (
                  <div className="absolute inset-0 bg-white/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  )
}