import React, { useState } from 'react'
import { use<PERSON>orm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiSave, FiMail, FiGlobe, FiDollarSign, FiShield } from 'react-icons/fi'
import { Button } from '@/components/common/Button'
import { Input } from '@/components/common/Input'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card'
import { AdminSidebar } from '@/components/layout/AdminSidebar'
import { api } from '@/services/api'

const settingsSchema = z.object({
  siteName: z.string().min(1, 'Site name is required'),
  siteUrl: z.string().url('Invalid URL'),
  adminEmail: z.string().email('Invalid email address'),
  supportEmail: z.string().email('Invalid email address'),
  smtpHost: z.string().min(1, 'SMTP host is required'),
  smtpPort: z.number().int().positive('Port must be a positive integer'),
  smtpUser: z.string().min(1, 'SMTP user is required'),
  smtpPass: z.string().min(1, 'SMTP password is required'),
  currency: z.string().min(1, 'Currency is required'),
  taxRate: z.number().min(0, 'Tax rate must be non-negative').max(100, 'Tax rate cannot exceed 100%'),
  registrationEnabled: z.boolean(),
})

type SettingsFormData = z.infer<typeof settingsSchema>

export default function AdminSettings() {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { register, handleSubmit, formState: { errors } } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      siteName: 'Furniture Designer',
      siteUrl: 'https://furnituredesigner.com',
      adminEmail: '<EMAIL>',
      supportEmail: '<EMAIL>',
      smtpHost: 'smtp.example.com',
      smtpPort: 587,
      smtpUser: 'smtpuser',
      smtpPass: '',
      currency: 'USD',
      taxRate: 10,
      registrationEnabled: true,
    },
  })

  const onSubmit: SubmitHandler<SettingsFormData> = async (data) => {
    setIsSubmitting(true)
    try {
      await api.put('/admin/settings', data)
      alert('Settings updated successfully')
    } catch (error) {
      console.error('Error updating settings:', error)
      alert('Failed to update settings. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <main className="flex-1 p-8 overflow-y-auto">
        <h1 className="text-4xl font-bold text-foreground mb-8">Admin Settings</h1>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FiGlobe className="mr-2" />
                  General Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Site Name"
                  {...register('siteName')}
                  error={errors.siteName?.message}
                />
                <Input
                  label="Site URL"
                  {...register('siteUrl')}
                  error={errors.siteUrl?.message}
                />
                <Input
                  label="Admin Email"
                  type="email"
                  {...register('adminEmail')}
                  error={errors.adminEmail?.message}
                />
                <Input
                  label="Support Email"
                  type="email"
                  {...register('supportEmail')}
                  error={errors.supportEmail?.message}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FiMail className="mr-2" />
                  Email Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="SMTP Host"
                  {...register('smtpHost')}
                  error={errors.smtpHost?.message}
                />
                <Input
                  label="SMTP Port"
                  type="number"
                  {...register('smtpPort', { valueAsNumber: true })}
                  error={errors.smtpPort?.message}
                />
                <Input
                  label="SMTP User"
                  {...register('smtpUser')}
                  error={errors.smtpUser?.message}
                />
                <Input
                  label="SMTP Password"
                  type="password"
                  {...register('smtpPass')}
                  error={errors.smtpPass?.message}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FiDollarSign className="mr-2" />
                  Financial Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input
                  label="Currency"
                  {...register('currency')}
                  error={errors.currency?.message}
                />
                <Input
                  label="Tax Rate (%)"
                  type="number"
                  step="0.01"
                  {...register('taxRate', { valueAsNumber: true })}
                  error={errors.taxRate?.message}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FiShield className="mr-2" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="registrationEnabled"
                    {...register('registrationEnabled')}
                    className="rounded border-gray-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor="registrationEnabled" className="text-sm font-medium text-gray-700">
                    Enable User Registration
                  </label>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
              <FiSave className="mr-2" />
              {isSubmitting ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </form>
      </main>
    </div>
  )
}