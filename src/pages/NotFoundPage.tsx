import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
// Using direct document.title approach instead of Helmet
import { FiHome } from 'react-icons/fi';

const NotFoundPage: React.FC = () => {
  const { t } = useTranslation('notFound');

  useEffect(() => {
    // Set page title directly
    document.title = t('seo.title');
    
    // To handle meta description, you would need a more comprehensive solution
    // but this handles the immediate title issue
  }, [t]);

  return (
    <>
      {/* SEO managed via direct DOM manipulation */}
      <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <h1 className="text-6xl font-bold text-primary mb-4">404</h1>
          <h2 className="text-3xl font-semibold text-gray-800 mb-6">{t('title')}</h2>
          <p className="text-xl text-gray-600 mb-8">{t('message')}</p>
          <Link to="/auth" className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary-dark transition duration-150 ease-in-out">
            <span className="mr-2"><FiHome /></span>
            {t('homeButton')}
          </Link>
        </motion.div>
      </div>
    </>
  );
};

export default NotFoundPage;
