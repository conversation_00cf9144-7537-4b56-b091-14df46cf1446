import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { Button } from '@/components/common/Button';
// Direct DOM manipulation for meta tags instead of Helmet
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

const LandingPage: React.FC = () => {
  const { t, i18n } = useTranslation(['landingPage', 'footer']);
  const { user } = useAuth();

  useEffect(() => {
    // Set document title
    document.title = t('seo.title');
    
    // Set meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.setAttribute('name', 'description');
      document.head.appendChild(metaDescription);
    }
    metaDescription.setAttribute('content', t('seo.description'));
    
    // Set meta keywords
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta');
      metaKeywords.setAttribute('name', 'keywords');
      document.head.appendChild(metaKeywords);
    }
    metaKeywords.setAttribute('content', t('seo.keywords'));
    
    // Update canonical link
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', `https://kapsulmobilya.com.tr/${i18n.language}`);
    
    // Set Open Graph meta tags
    updateMetaTag('og:title', t('seo.ogTitle'));
    updateMetaTag('og:description', t('seo.ogDescription'));
    updateMetaTag('og:image', 'https://kapsulmobilya.com.tr/og-image.jpg');
    updateMetaTag('og:url', `https://kapsulmobilya.com.tr/${i18n.language}`);
    
    // Set Twitter meta tag
    updateMetaTag('twitter:card', 'summary_large_image', 'name');
    
    function updateMetaTag(property: string, content: string, attributeType: 'property' | 'name' = 'property') {
      let meta = document.querySelector(`meta[${attributeType}="${property}"]`);
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attributeType, property);
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    }
    
    // Clean up function to reset meta tags when component unmounts
    return () => {
      document.title = 'Kapsul Mobilya';
    };
  }, [t, i18n.language]);

  return (
    <>
      {/* SEO meta tags handled via useEffect */}

      <div className="flex flex-col min-h-screen">
        <main className="flex-grow">
          <section className="relative h-screen">
            <video
              className="w-full h-full object-cover"
              autoPlay
              loop
              muted
              playsInline
            >
              <source src="https://assets.mixkit.co/videos/preview/mixkit-set-of-plateaus-seen-from-the-heights-in-a-sunset-26070-large.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <div className="absolute inset-0 bg-gradient-to-r from-black to-transparent flex items-center justify-start px-8 md:px-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="max-w-2xl"
              >
                <h1 className="text-5xl md:text-7xl text-white font-bold mb-6 leading-tight">
                  {t('heroTitle')}
                </h1>
                <p className="text-xl md:text-2xl text-gray-200 mb-8">
                  {t('heroSubtitle')}
                </p>
                <div className="flex gap-4">
                  {user ? (
                    <Link to="/store/design">
                      <Button
                        variant="default"
                        size="lg"
                        className="flex items-center bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        {t('designButton')}
                        <span className="ml-2"><FiArrowRight /></span>
                      </Button>
                    </Link>
                  ) : (
                    <Link to="/auth">
                      <Button
                        variant="default"
                        size="lg"
                        className="flex items-center bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                      >
                        {t('loginButton')}
                        <span className="ml-2"><FiArrowRight /></span>
                      </Button>
                    </Link>
                  )}
                </div>
              </motion.div>
            </div>
          </section>

          <section className="py-24 bg-gradient-to-b from-gray-100 to-white">
            <div className="container mx-auto px-4">
              <div className="flex flex-col gap-12 md:flex-row items-center justify-between">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="md:w-1/2 mb-12 md:mb-0"
                >
                  <h2 className="text-4xl font-bold mb-6">{t('visionTitle')}</h2>
                  <p className="text-xl leading-relaxed text-gray-700">
                    {t('visionDescription')}
                  </p>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="md:w-1/2"
                >
                  <img src="https://images.unsplash.com/photo-1524758631624-e2822e304c36?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&h=1200&q=80" alt="Our Vision" className="rounded-lg shadow-xl w-full h-96 object-cover" />
                </motion.div>
              </div>
            </div>
          </section>

          <section className="py-24 bg-primary text-white">
            <div className="container mx-auto px-4">
              <div className="flex flex-col gap-12 md:flex-row-reverse items-center justify-between">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="md:w-1/2 mb-12 md:mb-0"
                >
                  <h2 className="text-4xl font-bold mb-6">{t('missionTitle')}</h2>
                  <p className="text-xl leading-relaxed">
                    {t('missionDescription')}
                  </p>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  className="md:w-1/2"
                >
                  <img src="https://images.unsplash.com/photo-1618221195710-dd6b41faaea6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&h=1200&q=80" alt="Our Mission" className="rounded-lg shadow-xl w-full h-96 object-cover" />
                </motion.div>
              </div>
            </div>
          </section>

          <section className="py-24 bg-gray-100">
            <div className="container mx-auto px-4">
              <h2 className="text-4xl font-bold text-center mb-16">{t('featuresTitle')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
                {['feature1', 'feature2', 'feature3'].map((feature, index) => (
                  <motion.div
                    key={feature}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.2 }}
                    viewport={{ once: true }}
                    className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                  >
                    <h3 className="text-2xl font-semibold mb-4">{t(`${feature}Title`)}</h3>
                    <p className="text-gray-600">{t(`${feature}Description`)}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        </main>

        <footer className="bg-gray-800 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-2xl font-bold mb-4">{t('companyName', { ns: 'footer' })}</h3>
                <p className="text-gray-400">{t('allRightsReserved', { ns: 'footer' })}</p>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">{t('socialMedia.followUs', { ns: 'footer' })}</h3>
                <ul className="space-y-2">
                  <li><a href="#" className="hover:text-primary transition-colors duration-200">{t('socialMedia.facebook', { ns: 'footer' })}</a></li>
                  <li><a href="#" className="hover:text-primary transition-colors duration-200">{t('socialMedia.instagram', { ns: 'footer' })}</a></li>
                  <li><a href="#" className="hover:text-primary transition-colors duration-200">{t('socialMedia.twitter', { ns: 'footer' })}</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">{t('legalLinks.title', { ns: 'footer' })}</h3>
                <ul className="space-y-2">
                  <li><Link to="/privacy-policy" className="hover:text-primary transition-colors duration-200">{t('legalLinks.privacyPolicy', { ns: 'footer' })}</Link></li>
                  <li><Link to="/terms-of-service" className="hover:text-primary transition-colors duration-200">{t('legalLinks.termsOfService', { ns: 'footer' })}</Link></li>
                  <li><Link to="/cookie-policy" className="hover:text-primary transition-colors duration-200">{t('legalLinks.cookiePolicy', { ns: 'footer' })}</Link></li>
                </ul>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700 text-center">
              <p>{t('copyright', { ns: 'footer', year: new Date().getFullYear() })}</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
};

export default LandingPage;