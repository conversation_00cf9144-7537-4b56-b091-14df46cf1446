import { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

type LoginData = {
  username: string
  password: string
}

type AuthFormProps = {
  onLogin: (data: LoginData) => Promise<void>
}

export function AuthForm({ onLogin }: AuthFormProps) {
  const navigate = useNavigate()
  const location = useLocation()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState<LoginData>({
    username: '',
    password: ''
  })

  // Get the path to redirect to after login (if provided)
  const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/store/design'

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.username || !formData.password) {
      setError('Please enter both username and password')
      return
    }

    try {
      setIsLoading(true)
      await onLogin(formData)
      navigate(from)
    } catch (err: any) {
      console.error('Login error:', err)
      setError(err?.response?.data?.message || 'Invalid credentials')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center relative">
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-white to-pink-50">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-100/20 to-pink-100/20 animate-gradient"></div>
        <div className="absolute inset-0" style={{ backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.1) 1px, transparent 0px)', backgroundSize: '20px 20px' }}></div>
      </div>

      <div className="relative z-10 flex flex-col items-center w-full px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-12 transform scale-100 hover:scale-105 transition-transform duration-300">
          <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-20 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
          <img src="/logo.png" alt="Kapsül Mobilya Logo" className="h-24 w-auto relative drop-shadow-[0_0_15px_rgba(0,0,0,0.1)]" />
        </div>

        <div className="rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md w-full max-w-md bg-white/90 backdrop-blur-sm border border-gray-100 shadow-xl hover:shadow-2xl transition-shadow duration-300">
          <div className="flex flex-col space-y-1.5 p-6 text-center pb-2">
            <h3 className="text-2xl font-semibold leading-none tracking-tight text-primary text-3xl font-extrabold text-transparent pb-2 bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600">
              Giriş Yap
            </h3>
          </div>

          <div className="p-6 pt-0">
            <form className="space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                  {error}
                </div>
              )}

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">E-posta</label>
                <div className="relative mt-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                      <polyline points="22,6 12,13 2,6"></polyline>
                    </svg>
                  </div>
                  <input
                    className="w-full bg-white border border-gray-300 text-gray-900 placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-300 rounded-lg py-3 pl-10"
                    type="email"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-1">
                <label className="block text-sm font-medium text-gray-700">Şifre</label>
                <div className="relative mt-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg stroke="currentColor" fill="none" strokeWidth="2" viewBox="0 0 24 24" strokeLinecap="round" strokeLinejoin="round" className="text-purple-500" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    </svg>
                  </div>
                  <input
                    className="w-full bg-white border border-gray-300 text-gray-900 placeholder-gray-400 focus:border-purple-500 focus:ring-purple-500/20 transition-all duration-300 rounded-lg py-3 pl-10"
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <button
                className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4 w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500 text-white font-medium py-2.5 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span className="mr-2">Giriş Yapılıyor</span>
                    <div className="h-4 w-4 border-t-2 border-b-2 border-white rounded-full animate-spin"></div>
                  </>
                ) : 'Giriş Yap'}
              </button>
            </form>
          </div>
        </div>

        <div className="mt-8 text-center text-sm text-gray-600">
          <p>© {new Date().getFullYear()} Kapsül Mobilya. Tüm hakları saklıdır</p>
        </div>
      </div>
    </div>
  )
}