import { useNavigate } from 'react-router-dom';
import { FiHome, FiArrowLeft } from 'react-icons/fi';
import { Button } from '@/components/common/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import { useTranslation } from 'react-i18next';

export default function NotFound() {
  const navigate = useNavigate();
  const { t } = useTranslation(['common']);

  return (
    <div className="container mx-auto px-4 py-16 flex items-center justify-center min-h-[70vh]">
      <Card className="max-w-lg w-full">
        <CardHeader className="text-center">
          <div className="text-8xl font-bold text-primary mb-4">404</div>
          <CardTitle className="text-3xl">{t('pageNotFound', 'Page Not Found')}</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="text-gray-600 mb-8">
            {t('pageNotFoundMessage', "Sorry, we couldn't find the page you're looking for. It might have been moved or deleted.")}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={() => navigate('/')} className="flex items-center justify-center">
              <FiHome className="mr-2" />
              {t('backToHome', 'Back to Home')}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => navigate(-1)} 
              className="flex items-center justify-center"
            >
              <FiArrowLeft className="mr-2" />
              {t('goBack', 'Go Back')}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
