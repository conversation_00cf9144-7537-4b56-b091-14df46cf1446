import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useCart } from '@/hooks/useCart';
import { backendService, CreateOrderPayload } from '@/services/api';
import {
  FiShoppingBag, FiCheck, FiAlertCircle,
  FiPackage, FiUser, FiFileText, FiChevronDown,
  FiChevronUp, FiInfo, FiStar, FiDollarSign
} from 'react-icons/fi';

const CheckoutPage = () => {
  const { t } = useTranslation(['cart']);
  const navigate = useNavigate();
  const { cartItems, clearCart } = useCart();
  const [isProcessing, setIsProcessing] = useState(false);
  const [acceptContract, setAcceptContract] = useState(false);
  const [showContractModal, setShowContractModal] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!cartItems || cartItems.length === 0) {
      navigate('/');
    }
  }, [cartItems, navigate]);

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (item.design?.totalPrice || 0) * item.quantity;
    }, 0);
  };

  const formatFabricDisplay = (fabric: { option: string; color: string }) => {
    if (!fabric) return '-';

    const formatText = (text: string) => {
      return text
        .replace(/([A-Z])/g, ' $1')
        .replace(/([0-9]+)/g, ' $1')
        .trim()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');
    };

    return `${formatText(fabric.option)} - ${formatText(fabric.color)}`;
  };

  const toggleItemExpand = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handlePlaceOrder = async () => {
    if (!acceptContract) {
      setShowContractModal(true);
      return;
    }

    setIsProcessing(true);

    try {
      // Group cart items by customer (name + surname)
      const customerGroups: any = new Map();

      cartItems.forEach((item, index) => {
        const customerKey = `${item.design?.customerName || 'Guest'}_${item.design?.customerSurname || ''}`;
        if (!customerGroups.has(customerKey)) {
          customerGroups.set(customerKey, []);
        }
        customerGroups.get(customerKey).push({
          ...item,
          originalIndex: index
        });
      });

      const createdOrders = [];

      // Create separate order for each customer with complete data
      for (const [customerKey, customerItems] of customerGroups.entries()) {
        const [customerName, customerSurname] = customerKey.split('_');

        // Calculate customer total
        const customerTotal = customerItems.reduce((sum: any, item: any) => {
          const itemPrice = (item.design?.totalPrice || 0);
          const quantity = (item.quantity || 1);
          const itemTotal = itemPrice * quantity;
          console.log(`💰 Item pricing:`, {
            customerName: item.design?.customerName,
            itemPrice,
            quantity,
            itemTotal,
            designTotalPrice: item.design?.totalPrice
          });
          return sum + itemTotal;
        }, 0);

        console.log(`💰 Customer total for ${customerName} ${customerSurname}: ${customerTotal}`);

        // Prepare complete order data
        const orderPayload: CreateOrderPayload = {
          customerInfo: {
            name: customerName || '',
            surname: customerSurname || ''
          },
          items: customerItems.map((item: any) => ({
            design: {
              ...item.design,
              customerName: customerName || '',
              customerSurname: customerSurname || ''
            },
            quantity: item.quantity || 1,
            unitPrice: item.design?.totalPrice || 0,
            totalPrice: (item.design?.totalPrice || 0) * (item.quantity || 1)
          })),
          orderTotal: customerTotal,
          notes: `Order created from frontend with ${customerItems.length} items`,
          useDirectData: true // Tell backend to use our data directly
        };

        console.log(`Creating order for ${customerName} ${customerSurname}:`, orderPayload);

        // Since backend still expects cart items, we need to add items to cart first
        try {
          // Clear backend cart first
          try {
            await backendService.clearCart();
          } catch (error) {
            console.log('Cart clear error (may be empty):', error);
          }

          // Add this customer's items to cart with correct quantities
          for (const item of customerItems) {
            const designData = {
              ...item.design,
              totalPrice: item.design?.totalPrice || 0,
              customerName: customerName || '',
              customerSurname: customerSurname || ''
            };

            // Add item with its specific quantity (add each item individually)
            for (let i = 0; i < (item.quantity || 1); i++) {
              try {
                await backendService.addCustomDesignToCart(designData);
              } catch (error) {
                console.error('Failed to add item to cart:', error);
              }
            }
          }

          // Now create order from cart (with additional metadata)
          const response = await backendService.createOrder({
            customerInfo: {
              name: customerName || '',
              surname: customerSurname || ''
            },
            expectedTotal: customerTotal,
            notes: `Order created from frontend with ${customerItems.length} items`
          });

          createdOrders.push({
            ...response,
            customer: {
              name: customerName || '',
              surname: customerSurname || ''
            },
            items: orderPayload.items,
            totalAmount: customerTotal
          });

          console.log(`Order created successfully for ${customerName} ${customerSurname}:`, response);
        } catch (error: any) {
          console.error(`Failed to create order for ${customerName} ${customerSurname}:`, error);
          throw new Error(`Failed to create order for ${customerName} ${customerSurname}: ${error.message}`);
        }
      }

      // Save all created orders for confirmation page
      const orderData = {
        orders: createdOrders,
        totalAmount: calculateTotal(),
        orderDate: new Date().toISOString()
      };

      localStorage.setItem('lastOrder', JSON.stringify(orderData));

      // Clear the local cart
      await clearCart();

      // Navigate to confirmation
      navigate('/store/order-confirmation');
    } catch (error: any) {
      console.error('Order failed:', error);
      alert(t('errors.orderFailed') || `Order creation failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-purple-200 rounded-full opacity-10 blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-pink-200 rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            {t('checkout')}
          </h1>
          <p className="text-xl text-gray-600">
            {t('reviewAndConfirm', { defaultValue: 'Review and confirm your order' })}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Items with Full Details */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
                <h2 className="text-2xl font-bold flex items-center gap-3">
                  <FiPackage className="w-7 h-7" />
                  {t('orderItems', { defaultValue: 'Order Items' })}
                </h2>
              </div>

              <div className="p-6 space-y-6">
                {cartItems.map((item: any, index) => (
                  <div key={index} className="border border-gray-200 rounded-2xl overflow-hidden hover:shadow-lg transition-all duration-300">
                    {/* Item Header */}
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6">
                      <div className="flex items-center gap-4">
                        {/* Product Image */}
                        <div className="relative">
                          <div className="w-24 h-24 bg-gradient-to-br from-indigo-400 via-purple-400 to-pink-400 rounded-xl flex items-center justify-center shadow-lg">
                            <FiPackage className="w-12 h-12 text-white" />
                          </div>
                          <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-1">
                            <FiCheck className="w-3 h-3" />
                          </div>
                        </div>

                        {/* Product Info */}
                        <div className="flex-grow">
                          <h3 className="font-bold text-xl text-gray-900">
                            {item.design?.customerName && item.design?.customerSurname ?
                              `${item.design.customerName} ${item.design.customerSurname}` :
                              (item.design?.model || t('model'))}
                          </h3>
                          <div className="flex items-center gap-3 mt-1">
                            <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                              {item.design?.armchairType || '-'}
                            </span>
                            <span className="text-sm text-gray-600">
                              {t('quantity')}: <span className="font-semibold">{item.quantity}</span>
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            {t('barcode')}: <span className="font-mono font-medium">{item.design?.barcode || '-'}</span>
                            {item.design?.customerName && (
                              <span className="ml-3">
                                <FiUser className="inline w-3 h-3 mr-1" />
                                {item.design.customerName} {item.design.customerSurname || ''}
                              </span>
                            )}
                          </p>
                        </div>

                        {/* Price */}
                        <div className="text-right">
                          <p className="text-sm text-gray-600">{t('subtotal', { defaultValue: 'Subtotal' })}</p>
                          <p className="text-2xl font-bold text-purple-600">
                            ₺{((item.design?.totalPrice || 0) * item.quantity).toLocaleString('tr-TR', {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2
                            })}
                          </p>
                        </div>
                      </div>

                      {/* Toggle Details Button */}
                      <button
                        onClick={() => toggleItemExpand(`item-${index}`)}
                        className="mt-4 w-full py-2 px-4 bg-gradient-to-r from-purple-100 to-pink-100 hover:from-purple-200 hover:to-pink-200 text-purple-700 rounded-lg font-medium flex items-center justify-center gap-2 transition-all duration-300"
                      >
                        {expandedItems.has(`item-${index}`) ? (
                          <>
                            <FiChevronUp className="w-5 h-5" />
                            {t('hideDetails', { defaultValue: 'Hide Details' })}
                          </>
                        ) : (
                          <>
                            <FiChevronDown className="w-5 h-5" />
                            {t('showDetails', { defaultValue: 'Show Details' })}
                          </>
                        )}
                      </button>
                    </div>

                    {/* Expandable Details */}
                    {expandedItems.has(`item-${index}`) && (
                      <div className="p-6 bg-gradient-to-b from-white to-gray-50 border-t border-gray-200">
                        <h4 className="font-bold text-lg text-gray-900 mb-4 flex items-center gap-2">
                          <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
                          {t('configurationDetails', { defaultValue: 'Configuration Details' })}
                        </h4>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Conditional Structure Details */}
                          {(item.design?.model === 'bergere' || item.design?.armchairType === 'bergere') ? (
                            /* Bergere Structure Details */
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                              <h5 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                                <div className="w-8 h-8 bg-blue-500 text-white rounded-lg flex items-center justify-center">
                                  <FiPackage className="w-4 h-4" />
                                </div>
                                {t('bergereStructure', { defaultValue: 'Bergere Structure' })}
                              </h5>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('skeletonType', { defaultValue: 'Skeleton Type' })}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.skeleton?.type || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('skeletonColor', { defaultValue: 'Skeleton Color' })}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.skeleton?.color || '-'}</span>
                                </div>
                              </div>
                            </div>
                          ) : (
                            /* Armchair Structure Details */
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                              <h5 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                                <div className="w-8 h-8 bg-blue-500 text-white rounded-lg flex items-center justify-center">
                                  <FiPackage className="w-4 h-4" />
                                </div>
                                {t('armchairStructure', { defaultValue: 'Armchair Structure' })}
                              </h5>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('legs')}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.legs || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('baseFrame')}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.baseFrame || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('armrest')}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.armrest?.main || '-'}</span>
                                </div>
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('seat')}:</span>
                                  <span className="font-semibold text-gray-900">{item.design?.seat?.option || '-'}</span>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Fabric Details */}
                          <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200">
                            <h5 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                              <div className="w-8 h-8 bg-purple-500 text-white rounded-lg flex items-center justify-center">
                                <FiStar className="w-4 h-4" />
                              </div>
                              {t('fabricDetails')}
                            </h5>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                <span className="text-gray-700 font-medium">{t('sharedFabric')}:</span>
                                <span className="font-semibold text-gray-900 text-right text-sm">
                                  {formatFabricDisplay(item.design?.sharedFabric)}
                                </span>
                              </div>
                              {(item.design?.model === 'bergere' || item.design?.armchairType === 'bergere') ? (
                                <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                  <span className="text-gray-700 font-medium">{t('skeletonFabric', { defaultValue: 'Skeleton Fabric' })}:</span>
                                  <span className="font-semibold text-gray-900 text-right text-sm">
                                    {formatFabricDisplay(item.design?.woodVeneerFabric)}
                                  </span>
                                </div>
                              ) : (
                                <>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('woodVeneerFabric')}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.woodVeneerFabric)}
                                    </span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('legFabric')}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.legFabric)}
                                    </span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>

                          {/* Cushions if available */}
                          {item.design?.cushions && item.design.cushions.length > 0 && (
                            <div className="lg:col-span-2 bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-xl border border-teal-200">
                              <h5 className="font-semibold text-teal-900 mb-3 flex items-center gap-2">
                                <div className="w-8 h-8 bg-teal-500 text-white rounded-lg flex items-center justify-center">
                                  <FiStar className="w-4 h-4" />
                                </div>
                                {t('cushions')} ({item.design.cushions.length})
                              </h5>
                              <div className="grid grid-cols-1 gap-2">
                                {item.design.cushions.map((cushion: any, cushionIndex: number) => (
                                  <div key={cushionIndex} className="flex items-center justify-between p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('cushion', { defaultValue: 'Cushion' })} {cushionIndex + 1}:</span>
                                    <span className="font-semibold text-gray-900 text-sm">
                                      {cushion.cushionType} • {cushion.fabric} • {cushion.color}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden sticky top-4">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
                <h2 className="text-2xl font-bold">{t('orderSummary')}</h2>
              </div>

              <div className="p-6 space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between text-gray-700">
                    <span>{t('items', { defaultValue: 'Items' })} ({cartItems.length})</span>
                    <span className="font-semibold">
                      ₺{calculateTotal().toLocaleString('tr-TR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                      })}
                    </span>
                  </div>
                  <div className="pt-3 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <span className="text-xl font-bold text-gray-900">{t('total')}</span>
                      <span className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        ₺{calculateTotal().toLocaleString('tr-TR', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Sales Contract */}
                <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-4 rounded-xl border border-blue-200">
                  <div className="flex items-start gap-3">
                    <FiFileText className="w-5 h-5 text-blue-600 flex-shrink-0 mt-1" />
                    <div className="flex-grow">
                      <h4 className="font-semibold text-gray-900 mb-1">{t('salesContract')}</h4>
                      <p className="text-xs text-gray-600 mb-3">{t('contractNote', { defaultValue: 'Please read and accept our sales contract' })}</p>
                      <label className="flex items-start gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={acceptContract}
                          onChange={(e) => setAcceptContract(e.target.checked)}
                          className="mt-1 w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                        />
                        <span className="text-sm text-gray-700">{t('acceptSalesContract')}</span>
                      </label>
                      <button
                        onClick={() => setShowContractModal(true)}
                        className="text-sm text-blue-600 hover:text-blue-700 mt-2 underline"
                      >
                        {t('readContract', { defaultValue: 'Read full contract' })}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Place Order Button */}
                <button
                  onClick={handlePlaceOrder}
                  disabled={isProcessing || !acceptContract}
                  className={`w-full py-4 px-6 rounded-xl font-bold text-lg transition-all duration-300 flex items-center justify-center gap-3 ${acceptContract && !isProcessing
                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 shadow-xl hover:shadow-2xl transform hover:scale-[1.02]'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                      {t('placingOrder')}
                    </>
                  ) : (
                    <>
                      <FiCheck className="w-6 h-6" />
                      {t('placeOrder')}
                    </>
                  )}
                </button>

                {!acceptContract && (
                  <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
                    <FiAlertCircle className="w-4 h-4" />
                    <span>{t('acceptContractRequired', { defaultValue: 'Please accept the sales contract' })}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contract Modal */}
      {showContractModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShowContractModal(false)}></div>
          <div className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-6">
              <h3 className="text-2xl font-bold">{t('salesContract')}</h3>
            </div>
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                {t('salesContractText')}
              </p>
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end gap-4">
              <button
                onClick={() => setShowContractModal(false)}
                className="px-6 py-2 border-2 border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
              >
                {t('close', { defaultValue: 'Close' })}
              </button>
              <button
                onClick={() => {
                  setAcceptContract(true);
                  setShowContractModal(false);
                }}
                className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all"
              >
                {t('acceptAndClose', { defaultValue: 'Accept & Close' })}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CheckoutPage;