import React, { useState, useEffect, JSX } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiShoppingCart, FiRefreshCw } from 'react-icons/fi';
import { Button } from '@/components/common/Button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card';
import BabylonScene from '@/components/store/BabylonScene';
import { useCart } from '@/hooks/useCart';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { api } from '@/services/api';
import FurnitureCustomizer from './FurnitureCustomizer';

interface Design {
  furnitureType: 'armchair' | 'bergere';
  frameColor: string;
  upholstery: string;
  legType: string;
  cushionSize: string;
  cushionFabric: string;
  backCushion: string;
  decorativeCushions: number;
}

interface CustomizableProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  type: string;
  imageUrl: string;
  designOptions?: Record<string, unknown>;
}

export default function Customize(): JSX.Element {
  const location = useLocation();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { t } = useTranslation(['customize']);

  // Get product from location state or fetch it
  const [product, setProduct] = useState<CustomizableProduct | null>(
    (location.state as { product?: CustomizableProduct })?.product || null
  );

  const [loading, setLoading] = useState(!product);
  const [design, setDesign] = useState<Record<string, any>>({
    furnitureType: product?.type || 'armchair',
    frameColor: 'walnut',
    upholstery: 'cotton',
    legType: 'tapered',
    decorativeCushions: 0
  });

  // Price calculation based on customization
  const [basePrice, setBasePrice] = useState(product?.price || 0);
  const [additionalCost, setAdditionalCost] = useState(0);

  useEffect(() => {
    // If no product in state, get product ID from URL and fetch it
    const productId = new URLSearchParams(location.search).get('id');
    if (productId) {
      loadProduct()
    }
  }, [location.search])

  const loadProduct = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/products/${new URLSearchParams(location.search).get('id')}`)
      setProduct(response.data)
      setBasePrice(response.data.price)
    } catch (error) {
      console.error('Error loading product:', error)
      toast.error(t('errorLoadingProduct'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Calculate additional cost based on customization options
    if (product) {
      let additional = 0;

      // Premium upholstery costs more
      if (design.upholstery === 'leather') additional += 200;
      if (design.upholstery === 'velvet') additional += 150;

      // Special leg types cost more
      if (design.legType === 'hairpin') additional += 50;
      if (design.legType === 'turned') additional += 75;

      // Each decorative cushion costs extra
      additional += design.decorativeCushions * 25;

      setAdditionalCost(additional);
    }
  }, [design, product]);

  const updateDesign = (key: string, value: any) => {
    setDesign((prev: Partial<Design>) => ({ ...prev, [key]: value }))
    calculateAdditionalCost({ ...design, [key]: value })
  }

  const calculateAdditionalCost = (design: Partial<Design>) => {
    let additional = 0;

    // Premium upholstery costs more
    if (design.upholstery === 'leather') additional += 200;
    if (design.upholstery === 'velvet') additional += 150;

    // Special leg types cost more
    if (design.legType === 'hairpin') additional += 50;
    if (design.legType === 'turned') additional += 75;

    // Each decorative cushion costs extra
    additional += (design.decorativeCushions || 0) * 25;

    setAdditionalCost(additional);
  }

  const handleAddToCart = () => {
    if (product) {
      addToCart({
        id: `${product.id}-custom-${Date.now()}`,
        name: `${product.name} (${t('customized')})`,
        price: basePrice + additionalCost,
        quantity: 1,
        image: product.imageUrl,
        design: design
      });
      toast.success(t('addedToCartSuccess'));
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader>
            <CardTitle className="text-red-500">{t('productNotFound')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{t('couldNotFindProduct')}</p>
            <Button onClick={() => navigate('/products')} className="mt-4">
              {t('browseProducts')}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Button
        variant="outline"
        onClick={() => navigate('/products')}
        className="mb-6"
      >
        <span className="mr-2"><FiArrowLeft /></span> {t('backToProducts')}
      </Button>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Product Info */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>{product.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{product.description}</p>
              <div className="text-lg font-semibold flex justify-between items-center">
                <span>{t('basePrice')}:</span>
                <span>${basePrice.toFixed(2)}</span>
              </div>
              <div className="text-lg font-semibold flex justify-between items-center">
                <span>{t('additionalCost')}:</span>
                <span>${additionalCost.toFixed(2)}</span>
              </div>
              <div className="text-xl font-bold flex justify-between items-center mt-4 border-t pt-4">
                <span>{t('totalPrice')}:</span>
                <span>${(basePrice + additionalCost).toFixed(2)}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleAddToCart} className="w-full">
                <span className="mr-2"><FiShoppingCart /></span> {t('addToCart')}
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* 3D Model Viewer */}
        <div className="md:col-span-2">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>{t('preview')}</CardTitle>
            </CardHeader>
            <CardContent className="h-80">
              <div className="w-full h-full rounded-md overflow-hidden">
                <BabylonScene
                  design={design as any}
                  furnitureType={design.furnitureType}
                  getPartFilePath={(type: string, partType: string, name: string) => `/models/${type}/${partType}/${name}.glb`}
                  backCushion={design.backCushion}
                  decorativeCushions={design.decorativeCushions}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Customization Options */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>{t('customizeYourFurniture')}</CardTitle>
          </CardHeader>
          <CardContent>

          </CardContent>
        </Card>
      </div>
    </div>
  );
}
