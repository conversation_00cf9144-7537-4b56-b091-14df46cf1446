import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, FiShoppingCart, FiSearch, FiGrid, FiList } from 'react-icons/fi';
import { Button } from '@/components/common/Button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card';
import { Input } from '@/components/common/Input';
import { useCart } from '@/hooks/useCart';
import { api } from '@/services/api';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  type: string;
  imageUrl: string;
  designOptions?: Record<string, unknown>;
}

const categories = [
  { id: 'all', name: 'All Products' },
  { id: 'armchair', name: 'Armchairs' },
  { id: 'bergere', name: '<PERSON><PERSON>' },
  { id: 'sofa', name: '<PERSON><PERSON><PERSON>' },
  { id: 'ottoman', name: 'Ottomans' },
];

export default function Products() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [showFilters, setShowFilters] = useState(false);
  
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { t } = useTranslation(['products']);

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    filterProducts();
  }, [searchQuery, selectedCategory, products, priceRange]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await api.get('/products');
      setProducts(response.data);
      setFilteredProducts(response.data);
    } catch (err) {
      setError('Failed to load products');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const filterProducts = () => {
    let filtered = [...products];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        product => 
          product.name.toLowerCase().includes(query) || 
          product.description.toLowerCase().includes(query)
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(product => product.type === selectedCategory);
    }

    // Filter by price range
    filtered = filtered.filter(
      product => product.price >= priceRange[0] && product.price <= priceRange[1]
    );

    setFilteredProducts(filtered);
  };

  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.imageUrl,
      design: product.designOptions || {}
    });
  };

  const handleCustomize = (product: Product) => {
    navigate(`/customize`, { state: { product } });
  };

  const handlePriceRangeChange = (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newValue = parseInt(event.target.value, 10);
    const newRange = [...priceRange] as [number, number];
    newRange[index] = newValue;
    setPriceRange(newRange);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader>
            <CardTitle className="text-red-500">{t('errorLoadingProducts')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={fetchProducts} className="mt-4">
              {t('tryAgain')}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row justify-between items-start gap-8">
        {/* Filters Section */}
        <div className={`lg:w-1/4 w-full ${showFilters ? 'block' : 'hidden lg:block'}`}>
          <Card className="sticky top-24">
            <CardHeader>
              <CardTitle>{t('filters')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Categories */}
              <div>
                <h3 className="font-medium mb-2">{t('categories')}</h3>
                <div className="space-y-2">
                  {categories.map(category => (
                    <div 
                      key={category.id} 
                      className={`px-3 py-2 rounded-md cursor-pointer transition-colors ${
                        selectedCategory === category.id 
                          ? 'bg-primary text-white' 
                          : 'hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedCategory(category.id)}
                    >
                      {t(category.name)}
                    </div>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <h3 className="font-medium mb-2">{t('priceRange')}</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>{t('min')}: ${priceRange[0]}</span>
                    <span>{t('max')}: ${priceRange[1]}</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="10000"
                    step="100"
                    value={priceRange[0]}
                    onChange={(e) => handlePriceRangeChange(e, 0)}
                    className="w-full"
                  />
                  <input
                    type="range"
                    min="0"
                    max="10000"
                    step="100"
                    value={priceRange[1]}
                    onChange={(e) => handlePriceRangeChange(e, 1)}
                    className="w-full"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Products Section */}
        <div className="lg:w-3/4 w-full">
          {/* Search and View Options */}
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <div className="md:w-1/2 w-full relative">
              <Input
                placeholder={t('searchProducts')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode('grid')}
                className={viewMode === 'grid' ? 'bg-gray-100' : ''}
              >
                <FiGrid className="mr-1" />
                {t('grid')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode('list')}
                className={viewMode === 'list' ? 'bg-gray-100' : ''}
              >
                <FiList className="mr-1" />
                {t('list')}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden"
              >
                <FiFilter className="mr-1" />
                {t('filters')}
              </Button>
            </div>
          </div>

          {/* Product Count */}
          <div className="mb-4">
            <p className="text-gray-600">
              {t('showing')} {filteredProducts.length} {t('of')} {products.length} {t('products')}
            </p>
          </div>

          {/* Products Grid/List */}
          {filteredProducts.length === 0 ? (
            <div className="text-center py-16">
              <h3 className="text-xl font-medium mb-2">{t('noProductsFound')}</h3>
              <p className="text-gray-500 mb-4">{t('tryDifferentFilters')}</p>
              <Button onClick={() => {
                setSearchQuery('');
                setSelectedCategory('all');
                setPriceRange([0, 10000]);
              }}>
                {t('resetFilters')}
              </Button>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredProducts.map(product => (
                <Card key={product.id} className="h-full flex flex-col">
                  <div className="relative h-48 overflow-hidden">
                    <img 
                      src={product.imageUrl || '/placeholder-product.jpg'}
                      alt={product.name}
                      className="object-cover w-full h-full transition-transform hover:scale-105"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="pb-2 flex-grow">
                    <p className="text-gray-600 line-clamp-2">{product.description}</p>
                    <p className="text-xl font-semibold mt-2">${product.price.toFixed(2)}</p>
                  </CardContent>
                  <CardFooter className="pt-0 flex flex-col sm:flex-row gap-2">
                    <Button onClick={() => handleCustomize(product)} variant="outline" className="w-full">
                      {t('customize')}
                    </Button>
                    <Button onClick={() => handleAddToCart(product)} className="w-full">
                      <FiShoppingCart className="mr-2" /> {t('addToCart')}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredProducts.map(product => (
                <Card key={product.id} className="overflow-hidden">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/4 h-48 md:h-auto">
                      <img 
                        src={product.imageUrl || '/placeholder-product.jpg'}
                        alt={product.name}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div className="p-6 md:w-3/4 flex flex-col">
                      <CardTitle className="text-lg mb-2">{product.name}</CardTitle>
                      <p className="text-gray-600 mb-4 flex-grow">{product.description}</p>
                      <div className="flex flex-col sm:flex-row items-center justify-between">
                        <p className="text-xl font-semibold mb-4 sm:mb-0">${product.price.toFixed(2)}</p>
                        <div className="flex space-x-2">
                          <Button onClick={() => handleCustomize(product)} variant="outline">
                            {t('customize')}
                          </Button>
                          <Button onClick={() => handleAddToCart(product)}>
                            <FiShoppingCart className="mr-2" /> {t('addToCart')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
