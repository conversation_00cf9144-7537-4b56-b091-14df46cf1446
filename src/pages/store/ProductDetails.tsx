import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiShoppingCart, FiEdit, FiArrowLeft, FiHeart, FiCheck } from 'react-icons/fi';
import { Button } from '@/components/common/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import BabylonScene from '@/components/store/BabylonScene';
import { useCart } from '@/hooks/useCart';
import { api } from '@/services/api';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';

interface ProductDetails {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  price: number;
  type: string;
  imageUrl: string;
  additionalImages?: string[];
  specifications?: Record<string, string>;
  inStock: boolean;
  designOptions?: Record<string, unknown>;
  relatedProducts?: Array<{
    id: string;
    name: string;
    imageUrl: string;
    price: number;
  }>;
}

export default function ProductDetails() {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { t } = useTranslation(['products']);

  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeImage, setActiveImage] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      try {
        setLoading(true);
        const response = await api.get(`/products/${productId}`);
        setProduct(response.data);
        setActiveImage(response.data.imageUrl);
      } catch (err) {
        console.error('Error fetching product details:', err);
        setError(t('errorLoadingProduct', 'Failed to load product details'));
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId, t]);

  const handleAddToCart = () => {
    if (!product) return;

    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity,
      image: product.imageUrl,
      design: product.designOptions || {}
    });

    toast.success(t('addedToCart', 'Product added to cart'));
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < 1) return;
    setQuantity(newQuantity);
  };

  const handleCustomize = () => {
    if (!product) return;
    navigate(`/customize?id=${product.id}`);
  };

  const handleImageClick = (img: string, index: number): void => {
    setActiveImage(img);
  };

  const handleRelatedProductClick = (relatedProduct: { id: string; name: string; imageUrl: string; price: number }): void => {
    navigate(`/products/${relatedProduct.id}`);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-lg mx-auto">
          <CardHeader>
            <CardTitle className="text-red-500">{error || t('productNotFound', 'Product not found')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{t('errorMessage', 'We could not find the product you were looking for.')}</p>
            <Button onClick={() => navigate('/products')} className="mt-4">
              {t('backToProducts', 'Browse products')}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Button
        variant="outline"
        onClick={() => navigate('/products')}
        className="mb-6"
      >
        <span className="mr-2"><FiArrowLeft /></span> {t('backToProducts')}
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Product Images */}
        <div>
          <div className="w-full h-80 sm:h-96 bg-gray-100 rounded-lg mb-4 overflow-hidden">
            {activeImage ? (
              <img
                src={activeImage}
                alt={product.name}
                className="w-full h-full object-contain"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <BabylonScene
                  design={{
                    furnitureType: (product.type || 'armchair') as 'armchair' | 'bergere',
                    frameColor: 'brown',
                    upholstery: 'standard',
                    legType: 'wooden',
                    cushionSize: 'medium',
                    cushionFabric: 'grey',
                    backCushion: 'standard',
                    decorativeCushions: {
                      size: 'medium',
                      fabric: 'grey',
                      quantity: 0
                    },
                    addOns: [],
                    lowerFrame: 'standard',
                    sharedFabric: 'grey',
                    legs: 'wooden',
                    legFabric: 'grey',
                    woodVeneer: 'brown',
                    woodVeneerFabric: 'grey',
                    armrest: {
                      main: 'standard',
                      sub: []
                    },
                    cushion: {
                      option: 'standard'
                    },
                    seat: {
                      type: 'standard',
                      color: 'grey',
                      options: {
                        cektirme: false,
                        tekParca: false,
                        bombe: false,
                        kirisiklik: false,
                        kulak: false
                      }
                    },
                    fabric: {
                      type: 'grey',
                      color: '1'
                    }
                  }}
                  furnitureType={product.type || 'armchair'}
                  getPartFilePath={(type, partType, name) => `/assets/${type}/${partType}/${name}.glb`}
                  backCushion="standard"
                  decorativeCushions={{
                    size: 'medium',
                    fabric: 'grey',
                    quantity: 0
                  }}
                />
              </div>
            )}
          </div>

          {/* Additional Images */}
          {product.additionalImages && product.additionalImages.length > 0 && (
            <div className="grid grid-cols-5 gap-2">
              <div
                className={`border-2 rounded-md cursor-pointer ${activeImage === product.imageUrl ? 'border-primary' : 'border-transparent'}`}
                onClick={() => setActiveImage(product.imageUrl)}
              >
                <img
                  src={product.imageUrl}
                  alt={product.name}
                  className="w-full h-20 object-cover rounded"
                />
              </div>

              {product.additionalImages?.map((img: string, index: number) => (
                <div
                  key={index}
                  className={`border-2 rounded-md cursor-pointer ${activeImage === img ? 'border-primary' : 'border-transparent'}`}
                  onClick={() => handleImageClick(img, index)}
                >
                  <img
                    src={img}
                    alt={`${product.name} - View ${index + 1}`}
                    className="w-full h-16 object-cover"
                  />
                </div>
              ))}

              <div
                className={`border-2 rounded-md cursor-pointer ${activeImage === null ? 'border-primary' : 'border-transparent'}`}
                onClick={() => setActiveImage(null)}
              >
                <div className="w-full h-20 bg-gray-100 flex items-center justify-center text-gray-500 rounded">
                  {t('threeDView')}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div>
          <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
          <p className="text-2xl font-semibold text-primary mb-4">${product.price.toFixed(2)}</p>

          <div className="mb-6">
            <p className="text-gray-700 mb-4">{product.description}</p>
            {product.longDescription && (
              <p className="text-gray-600">{product.longDescription}</p>
            )}
          </div>

          {product.inStock ? (
            <div className="inline-flex items-center text-green-600 bg-green-100 px-3 py-1 rounded-full text-sm font-medium mb-6">
              <span className="mr-1"><FiCheck /></span> {t('inStock')}
            </div>
          ) : (
            <div className="inline-flex items-center text-red-600 bg-red-100 px-3 py-1 rounded-full text-sm font-medium mb-6">
              {t('outOfStock')}
            </div>
          )}

          {/* Quantity Selector */}
          <div className="flex items-center mb-6">
            <span className="text-gray-700 mr-4">{t('quantity')}:</span>
            <div className="flex items-center border rounded-md">
              <button
                className="px-3 py-1 border-r"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                -
              </button>
              <span className="px-4 py-1">{quantity}</span>
              <button
                className="px-3 py-1 border-l"
                onClick={() => handleQuantityChange(quantity + 1)}
              >
                +
              </button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 mb-8">
            <Button
              onClick={handleAddToCart}
              disabled={!product.inStock}
              className="flex-grow"
            >
              <span className="mr-2"><FiShoppingCart /></span> {t('addToCart')}
            </Button>
            <Button
              variant="outline"
              onClick={handleCustomize}
              className="flex-grow"
            >
              <span className="mr-2"><FiEdit /></span> {t('customize')}
            </Button>
            <Button variant="ghost" className="w-12 h-12 p-0 flex items-center justify-center">
              <span className="text-red-500"><FiHeart /></span>
            </Button>
          </div>

          {/* Specifications */}
          {product.specifications && Object.keys(product.specifications).length > 0 && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-lg">{t('specifications')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="py-2 flex justify-between">
                      <span className="text-gray-600">{key}:</span>
                      <span className="font-medium">{String(value)}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Related Products */}
      {product.relatedProducts && product.relatedProducts.length > 0 && (
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">{t('relatedProducts')}</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {product.relatedProducts.map((relatedProduct: { id: string; name: string; imageUrl: string; price: number }) => (
              <Card key={relatedProduct.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => handleRelatedProductClick(relatedProduct)}>
                <div className="h-40 overflow-hidden">
                  <img
                    src={relatedProduct.imageUrl || '/placeholder-product.jpg'}
                    alt={relatedProduct.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold line-clamp-1">{relatedProduct.name}</h3>
                  <p className="text-primary font-medium mt-1">${relatedProduct.price.toFixed(2)}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
