import { useEffect, useState } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { apiService } from '@/services/api'
import {
  FiSave, FiAlertCircle, FiUser, FiPhone, FiMapPin,
  FiTruck, FiPercent, FiBriefcase, FiHome, FiCheck,
  FiInfo, FiSettings, FiDollarSign
} from 'react-icons/fi'
import { toast } from 'react-toastify'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from 'react-i18next'

type StoreSetting = {
  id?: string
  storeId?: string
  companyName: string
  taxNumber: string
  ownerName: string
  ownerPhone: string
  storeAddress: string
  storePhone: string
  managerName: string
  managerPhone: string
  shippingCompany: string
  shippingPhone: string
  profitMargin: number
}

const StoreSettings = () => {
  const { t } = useTranslation('storeSettings')
  const [settings, setSettings] = useState<StoreSetting>({
    companyName: '',
    taxNumber: '',
    ownerName: '',
    ownerPhone: '',
    storeAddress: '',
    storePhone: '',
    managerName: '',
    managerPhone: '',
    shippingCompany: '',
    shippingPhone: '',
    profitMargin: 20 // Default profit margin
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false)

  // Fetch store settings
  const { data, isLoading, isError } = useQuery<StoreSetting>({
    queryKey: ['storeSettings'],
    queryFn: async () => {
      return apiService.get<StoreSetting>('/storeSettings')
    }
  })

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (updatedSettings: StoreSetting) => {
      return apiService.put<StoreSetting>('/storeSettings', updatedSettings)
    },
    onSuccess: () => {
      toast.success(t('settingsUpdatedSuccess'))
      setIsSubmitting(false)
      setHasChanges(false)
      setShowSuccessAnimation(true)
      setTimeout(() => setShowSuccessAnimation(false), 3000)
    },
    onError: (error) => {
      console.error('Error updating settings:', error)
      toast.error(t('settingsUpdateFailed'))
      setIsSubmitting(false)
    }
  })

  useEffect(() => {
    if (data) {
      setSettings(data)
    }
  }, [data])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    let processedValue: string | number = value

    // Handle numeric value for profit margin
    if (name === 'profitMargin') {
      processedValue = parseFloat(value) || 0
    }

    setSettings(prev => ({
      ...prev,
      [name]: processedValue
    }))
    setHasChanges(true)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    updateSettingsMutation.mutate(settings)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
        <div className="container mx-auto p-6 max-w-5xl">
          <div className="flex justify-center items-center h-96">
            <div className="relative">
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-200"></div>
              <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
            </div>
            <p className="ml-4 text-lg text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
        <div className="container mx-auto p-6 max-w-5xl">
          <div className="flex flex-col justify-center items-center h-96">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="mb-4 text-red-500"
            >
              <FiAlertCircle size={80} />
            </motion.div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('errorLoadingSettings', { defaultValue: 'Error Loading Settings' })}</h2>
            <p className="text-gray-600 text-center max-w-md">
              {t('errorLoadingSettingsDescription', { defaultValue: 'There was an error loading the settings. Please try again later.' })}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-xl hover:from-purple-700 hover:to-pink-700 transition-all font-semibold"
            >
              {t('tryAgain', { defaultValue: 'Try Again' })}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-20 w-96 h-96 bg-purple-200 rounded-full opacity-10 blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-pink-200 rounded-full opacity-10 blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto p-6 max-w-5xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mb-4 shadow-lg">
            <FiSettings className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            {t('storeSettings')}
          </h1>
          <p className="text-xl text-gray-600">{t('manageYourStoreInformation', { defaultValue: 'Manage your store information and business settings' })}</p>
        </motion.div>

        {/* Success Animation */}
        <AnimatePresence>
          {showSuccessAnimation && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="fixed top-20 right-20 bg-green-500 text-white px-6 py-3 rounded-xl shadow-lg flex items-center gap-2 z-50"
            >
              <FiCheck className="w-5 h-5" />
              <span className="font-semibold">{t('settingsUpdatedSuccess')}</span>
            </motion.div>
          )}
        </AnimatePresence>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Company Information */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl text-white">
                  <FiBriefcase className="w-6 h-6" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">{t('companyInformation')}</h2>
              </div>

              <div className="space-y-5">
                <div>
                  <label htmlFor="companyName" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('companyName')}
                  </label>
                  <div className="relative">
                    <input
                      id="companyName"
                      name="companyName"
                      type="text"
                      value={settings.companyName}
                      onChange={handleChange}
                      className="w-full pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="taxNumber" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('taxNumber')}
                  </label>
                  <input
                    id="taxNumber"
                    name="taxNumber"
                    type="text"
                    value={settings.taxNumber}
                    onChange={handleChange}
                    className="w-full pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="storeAddress" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('storeAddress')}
                  </label>
                  <div className="relative">
                    <div className="absolute top-3 left-3 text-gray-400">
                      <FiMapPin className="w-5 h-5" />
                    </div>
                    <textarea
                      id="storeAddress"
                      name="storeAddress"
                      rows={3}
                      value={settings.storeAddress}
                      onChange={handleChange}
                      className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all resize-none"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="storePhone" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('storePhone')}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center text-gray-400">
                      <FiPhone className="w-5 h-5" />
                    </div>
                    <input
                      id="storePhone"
                      name="storePhone"
                      type="tel"
                      value={settings.storePhone}
                      onChange={handleChange}
                      className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                      required
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl text-white">
                  <FiUser className="w-6 h-6" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">{t('contactInformation', { defaultValue: 'Contact Information' })}</h2>
              </div>

              <div className="space-y-5">
                <div>
                  <label htmlFor="ownerName" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('ownerName')}
                  </label>
                  <input
                    id="ownerName"
                    name="ownerName"
                    type="text"
                    value={settings.ownerName}
                    onChange={handleChange}
                    className="w-full pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="ownerPhone" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('ownerPhone')}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center text-gray-400">
                      <FiPhone className="w-5 h-5" />
                    </div>
                    <input
                      id="ownerPhone"
                      name="ownerPhone"
                      type="tel"
                      value={settings.ownerPhone}
                      onChange={handleChange}
                      className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="managerName" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('managerName')}
                  </label>
                  <input
                    id="managerName"
                    name="managerName"
                    type="text"
                    value={settings.managerName}
                    onChange={handleChange}
                    className="w-full pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="managerPhone" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('managerPhone')}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center text-gray-400">
                      <FiPhone className="w-5 h-5" />
                    </div>
                    <input
                      id="managerPhone"
                      name="managerPhone"
                      type="tel"
                      value={settings.managerPhone}
                      onChange={handleChange}
                      className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                      required
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Shipping Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-green-400 to-green-600 rounded-xl text-white">
                  <FiTruck className="w-6 h-6" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">{t('shippingInformation', { defaultValue: 'Shipping Information' })}</h2>
              </div>

              <div className="space-y-5">
                <div>
                  <label htmlFor="shippingCompany" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('shippingCompany')}
                  </label>
                  <input
                    id="shippingCompany"
                    name="shippingCompany"
                    type="text"
                    value={settings.shippingCompany}
                    onChange={handleChange}
                    className="w-full pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="shippingPhone" className="block text-sm font-semibold text-gray-700 mb-2">
                    {t('shippingPhone')}
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center text-gray-400">
                      <FiPhone className="w-5 h-5" />
                    </div>
                    <input
                      id="shippingPhone"
                      name="shippingPhone"
                      type="tel"
                      value={settings.shippingPhone}
                      onChange={handleChange}
                      className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                      required
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Business Settings */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-gradient-to-br from-orange-400 to-pink-600 rounded-xl text-white">
                  <FiDollarSign className="w-6 h-6" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">{t('businessSettings', { defaultValue: 'Business Settings' })}</h2>
              </div>

              <div>
                <label htmlFor="profitMargin" className="block text-sm font-semibold text-gray-700 mb-2">
                  {t('profitMargin')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-3 flex items-center text-gray-400">
                    <FiPercent className="w-5 h-5" />
                  </div>
                  <input
                    id="profitMargin"
                    name="profitMargin"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={settings.profitMargin}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-purple-500 focus:ring-4 focus:ring-purple-200 transition-all"
                    required
                  />
                </div>
                <div className="mt-3 p-4 bg-blue-50 rounded-xl flex items-start gap-3">
                  <FiInfo className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-blue-800">
                    {t('profitMarginDescription', { defaultValue: 'This percentage will be added to all product costs to determine selling price.' })}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Save Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-center mt-8"
          >
            <button
              type="submit"
              disabled={isSubmitting || !hasChanges}
              className={`
                flex items-center gap-3 px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300
                transform hover:scale-105 shadow-xl hover:shadow-2xl
                ${isSubmitting || !hasChanges
                  ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                  : 'bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700'
                }
              `}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-6 w-6 border-3 border-white border-t-transparent"></div>
                  <span>{t('saving')}</span>
                </>
              ) : (
                <>
                  <FiSave className="w-6 h-6" />
                  <span>{t('saveSettings')}</span>
                </>
              )}
            </button>
          </motion.div>

          {hasChanges && !isSubmitting && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center mt-4 text-amber-600 font-medium"
            >
              {t('unsavedChanges', { defaultValue: 'You have unsaved changes' })}
            </motion.p>
          )}
        </form>
      </div>
    </div>
  )
}

export default StoreSettings