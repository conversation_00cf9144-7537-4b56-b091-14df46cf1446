import React, { useEffect, useRef, useState } from 'react'
import * as BABYLON from '@babylonjs/core'
import '@babylonjs/loaders'

const designOptions = {
  legs: [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
  ],
  lowerFrame: ['Alt kasa'],
  woodVeneer: ['1', '2', '3'],
  armrest: {
    main: ['KOL 1', 'KOL 2', 'KOL 3', 'KOL 4', 'KOL 5', 'KOL 6', 'KOL 7'],
    sub: ['BOŞ', '3', '4', '5', '3-4', '3-5', '4-5', '3-4-5'],
  },
  cushion: {
    type: ['Kulaklı', 'Kulaksız'],
    option: ['1', '2', '3'],
    sub: ['Boş', '1', '3', '4', '1-3', '1-4', '3-4', '1-3-4'],
  },
  fabric: ['brown', 'green-cotton', 'grey'],
}

interface ArmchairModelProps {
  design: unknown
  scene: BABYLON.Scene
}

const ArmchairModel: React.FC<ArmchairModelProps> = ({ design, scene }: any) => {
  useEffect(() => {
    let armchair: BABYLON.Mesh // Keep a reference to the armchair mesh

    const loadArmchairParts = async () => {
      const basePath = '/assets/armchair'

      const loadAndPositionMesh = async (path: string, textures?: any) => {
        const isGLTF = path.endsWith('.glb') || path.endsWith('.gltf')

        let mesh
        const result = await BABYLON.SceneLoader.ImportMeshAsync(
          '',
          path,
          '',
          scene
        )
        mesh = result.meshes[0]

        if (textures && !isGLTF) {
          applyMaterialToMesh(mesh, textures)
        }
        return mesh
      }

      try {
        const [lowerFrame, legs, woodVeneer, armrest, cushion] =
          await Promise.all([
            loadAndPositionMesh(`${basePath}/objects/AltKasa/Alt kasa.glb`),
            loadAndPositionMesh(
              `${basePath}/objects/Ayaklar/${design.legs}.glb`
            ),
            loadAndPositionMesh(
              `${basePath}/objects/AhsapKasa/${design.woodVeneer}/${design.woodVeneer}.glb`
            ),
            loadAndPositionMesh(
              `${basePath}/objects/Kol/${design.armrest.main}/${design.armrest.sub}.glb`
            ),
            loadAndPositionMesh(
              `${basePath}/objects/Minder/${design.cushion.type}/${design.cushion.option}/${design.cushion.sub}.glb`
            ),
          ])

        // Create a parent mesh for the entire armchair
        armchair = new BABYLON.Mesh('armchair', scene)

          // Parent all parts to the armchair mesh
          ;[lowerFrame, legs, woodVeneer, armrest, cushion].forEach((mesh) => {
            mesh.parent = armchair
          })

        // Calculate the bounding box of the entire armchair
        const boundingInfo = armchair.getHierarchyBoundingVectors(true)
        const center = BABYLON.Vector3.Center(
          boundingInfo.min,
          boundingInfo.max
        )
        const size = boundingInfo.max.subtract(boundingInfo.min)

        // Adjust camera to focus on the armchair
        const camera = scene.getCameraByName(
          'armchairCamera'
        ) as BABYLON.ArcRotateCamera
        if (camera) {
          camera.setTarget(center)

          // Adjust the camera radius to fit the armchair in view
          const maxDimension = Math.max(size.x, size.y, size.z)
          camera.radius = maxDimension * 1.5 // Adjust the factor as needed
        }
      } catch (error) {
        console.error('Error loading armchair parts:', error)
      }
    }

    loadArmchairParts()

    return () => {
      // Dispose of the armchair mesh and its children
      if (armchair) {
        armchair.dispose()
      }
    }
  }, [design, scene])

  return null
}

const applyMaterialToMesh = (mesh: BABYLON.AbstractMesh, textures: any) => {
  const material: any = new BABYLON.StandardMaterial(
    'customMaterial',
    mesh.getScene()
  )
  material.diffuseTexture = textures.diffuse
  material.bumpTexture = textures.normal
  material.ambientTexture = textures.ao
  material.useHeightMap = true
  material.heightTexture = textures.height
  material.roughness = 0.8
  material.metallic = 0.2

  mesh.material = material
}

const BabylonScene: React.FC<{ design: any }> = ({ design }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [scene, setScene] = useState<BABYLON.Scene | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    const engine = new BABYLON.Engine(canvasRef.current, true)
    const scene = new BABYLON.Scene(engine)

    const camera = new BABYLON.ArcRotateCamera(
      'armchairCamera',
      Math.PI / 2,
      Math.PI / 2.5,
      10,
      BABYLON.Vector3.Zero(),
      scene
    )
    camera.attachControl(canvasRef.current, true)

    // Enable zooming
    camera.wheelPrecision = 50
    camera.lowerRadiusLimit = 5
    camera.upperRadiusLimit = 20

    // Add lights
    new BABYLON.HemisphericLight('light1', new BABYLON.Vector3(1, 1, 0), scene)
    new BABYLON.PointLight('light2', new BABYLON.Vector3(0, 1, -1), scene)

    setScene(scene)

    engine.runRenderLoop(() => {
      scene.render()
    })

    const resizeObserver = new ResizeObserver(() => {
      engine.resize()
    })
    resizeObserver.observe(canvasRef.current)

    return () => {
      resizeObserver.disconnect()
      engine.dispose()
    }
  }, [])

  return (
    <>
      <canvas ref={canvasRef} style={{ width: '100%', height: '500px' }} />
      {scene && <ArmchairModel design={design} scene={scene} />}
    </>
  )
}

const ArmchairCustomizer: React.FC = () => {
  const [design, setDesign] = useState({
    legs: '1',
    lowerFrame: 'Alt kasa',
    woodVeneer: '1',
    armrest: { main: 'KOL 1', sub: '3' },
    cushion: { type: 'Kulaklı', option: '1', sub: '1' },
    sharedFabric: 'grey',
    legFabric: 'brown',
  })

  const handleDesignChange = (
    part: string,
    subPart: string | null,
    value: string
  ) => {
    setDesign((prev: any) => ({
      ...prev,
      [part]: subPart
        ? {
          ...prev[part as keyof typeof prev],
          [subPart]: value,
        }
        : value,
    }))
  }

  return (
    <div className="flex h-screen bg-gray-100 w-full">
      <main className="flex-1 p-8 overflow-y-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          Armchair Design Tool
        </h1>
        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">
              Customize Your Armchair
            </h2>
          </div>
          <div className="flex flex-col gap-6">
            <div
              className="bg-gray-200 rounded-lg flex items-center justify-center p-4"
              style={{ height: '500px' }}
            >
              <BabylonScene design={design} />
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Legs
                  </label>
                  <select
                    value={design.legs}
                    onChange={(e) =>
                      handleDesignChange('legs', null, e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.legs.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Leg Fabric
                  </label>
                  <select
                    value={design.legFabric}
                    onChange={(e) =>
                      handleDesignChange('legFabric', null, e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.fabric.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Wood Veneer
                  </label>
                  <select
                    value={design.woodVeneer}
                    onChange={(e) =>
                      handleDesignChange('woodVeneer', null, e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.woodVeneer.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Armrest (Main/Sub)
                  </label>
                  <select
                    value={design.armrest.main}
                    onChange={(e) =>
                      handleDesignChange('armrest', 'main', e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.armrest.main.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  <select
                    value={design.armrest.sub}
                    onChange={(e) =>
                      handleDesignChange('armrest', 'sub', e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.armrest.sub.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cushion (Type/Option/Sub)
                  </label>
                  <select
                    value={design.cushion.type}
                    onChange={(e) =>
                      handleDesignChange('cushion', 'type', e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.cushion.type.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  <select
                    value={design.cushion.option}
                    onChange={(e) =>
                      handleDesignChange('cushion', 'option', e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.cushion.option.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  <select
                    value={design.cushion.sub}
                    onChange={(e) =>
                      handleDesignChange('cushion', 'sub', e.target.value)
                    }
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {designOptions.cushion.sub.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="col-span-2 md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Shared Fabric (Cushion, Armrest, Lower Frame)
                </label>
                <select
                  value={design.sharedFabric}
                  onChange={(e) =>
                    handleDesignChange('sharedFabric', null, e.target.value)
                  }
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {designOptions.fabric.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          <div className="mt-8 flex justify-between">
            <button className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Save Design
            </button>
            <button className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              Add to Cart
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default ArmchairCustomizer
