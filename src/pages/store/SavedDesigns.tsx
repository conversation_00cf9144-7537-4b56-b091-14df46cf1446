import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { backendService } from '@/services/api'
import { useNavigate } from 'react-router-dom'
import { useCart } from '@/hooks/useCart'
import { toast } from 'react-toastify'
import { motion, AnimatePresence } from 'framer-motion'
import { FiShoppingCart, FiTrash2, FiPackage, FiSearch, FiFilter, FiGrid, FiList, FiHeart, FiEdit3, FiX } from 'react-icons/fi'
import { useTranslation } from 'react-i18next'

// Import BabylonScene only if needed (commented out for performance)
// const BabylonScene = lazy(() => import('@/components/store/BabylonScene'))

type SavedDesign = {
  id: string
  userId: string
  name: string
  designJson: string
  createdAt: string
  updatedAt: string
  thumbnail?: string
  furnitureType?: string
  totalPrice?: number
}

type DesignData = {
  name: string
  type: string
  totalPrice: number
  legs?: string
  baseFrame?: string
  woodVeneer?: string
  armrest?: {
    main: string
    sub: string | string[]
    mainAhsap: boolean
  }
  seat?: {
    option: string
    sub: string
  }
  sharedFabric?: {
    option: string
    color: string
  }
  model?: string
  armchairType?: string
  furnitureType?: string
  fabric?: { type: string }
  arm?: { type: string }
  leg?: { type: string }
  legFabric?: {
    option: string
    color: string
  }
  woodVeneerFabric?: {
    option: string
    color: string
  }
  backPillow?: string
  cushions?: Array<{
    cushionType: string
    fabric: string
    color: string
  }>
  customerName?: string
  customerSurname?: string
  barcode?: string
}

const SavedDesigns = () => {
  const { t } = useTranslation('savedDesigns')
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { addToCart } = useCart()
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('name')
  const [selectedDesign, setSelectedDesign] = useState<SavedDesign | null>(null)

  // Fetch saved designs
  const { data: savedDesigns, isLoading, isError } = useQuery<SavedDesign[]>({
    queryKey: ['savedDesigns'],
    queryFn: async () => {
      return backendService.getSavedDesigns()
    }
  })

  // Delete design mutation
  const deleteDesignMutation = useMutation({
    mutationFn: async (designId: string) => {
      return backendService.deleteSavedDesign(designId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['savedDesigns'] })
      toast.success(t('designDeletedSuccessfully'))
      setSelectedDesign(null)
    },
    onError: (error) => {
      console.error('Error deleting design:', error)
      toast.error(t('failedToDeleteDesign'))
    }
  })

  const handleAddToCart = async (design: SavedDesign) => {
    try {
      const designData = parseDesignJson(design.designJson)
      await addToCart({
        design: designData,
        quantity: 1
      })
      toast.success(t('addedToCart'))
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error(t('failedToAddToCart'))
    }
  }

  const handleDeleteDesign = (designId: string) => {
    if (window.confirm(t('confirmDeleteDesign'))) {
      deleteDesignMutation.mutate(designId)
    }
  }

  const parseDesignJson = (designJson: string): DesignData => {
    try {
      const parsed = JSON.parse(designJson)
      console.log('Parsed design:', parsed)

      // Handle different possible data structures
      // If the design is wrapped in a 'design' property
      if (parsed.design) {
        return {
          ...parsed.design,
          name: parsed.design.customerName ? `${parsed.design.customerName} ${parsed.design.customerSurname || ''}`.trim() : 'Custom Design',
          type: parsed.design.model || parsed.design.furnitureType || 'Armchair'
        }
      }

      // Direct design structure
      return {
        ...parsed,
        name: parsed.customerName ? `${parsed.customerName} ${parsed.customerSurname || ''}`.trim() : 'Custom Design',
        type: parsed.model || parsed.furnitureType || 'Armchair'
      }
    } catch (e) {
      console.error('Failed to parse design JSON:', e, designJson)
      return { name: 'Custom Design', type: 'Unknown', totalPrice: 0 }
    }
  }


  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount)
  }

  const filterAndSortDesigns = (designs: SavedDesign[] = []) => {
    let filtered = designs

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(design => {
        const designData = parseDesignJson(design.designJson)
        return design.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          designData.type?.toLowerCase().includes(searchTerm.toLowerCase())
      })
    }

    // Sort designs
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'price-high': {
          const priceA = parseDesignJson(a.designJson).totalPrice || 0
          const priceB = parseDesignJson(b.designJson).totalPrice || 0
          return priceB - priceA
        }
        case 'price-low': {
          const priceLowA = parseDesignJson(a.designJson).totalPrice || 0
          const priceLowB = parseDesignJson(b.designJson).totalPrice || 0
          return priceLowA - priceLowB
        }
        default:
          return 0
      }
    })

    return filtered
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
        <div className="container mx-auto p-6 max-w-7xl">
          <div className="flex justify-center items-center h-64">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-purple-200"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-purple-600 absolute top-0 left-0"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
        <div className="container mx-auto p-6 max-w-7xl">
          <div className="flex flex-col justify-center items-center h-64">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-4 text-red-500"
            >
              <FiPackage size={64} />
            </motion.div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('errorLoadingDesigns')}</h2>
            <p className="text-gray-600 text-center max-w-md">
              {t('errorLoadingDesignsDescription')}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              {t('tryAgain')}
            </button>
          </div>
        </div>
      </div>
    )
  }

  const filteredDesigns = filterAndSortDesigns(savedDesigns)

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
            {t('savedDesigns')}
          </h1>
          <p className="text-gray-600">{t('savedDesignsDescription')}</p>
        </motion.div>

        {/* Filters and Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8"
        >
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-3 border border-gray-200 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:bg-white focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all"
                placeholder={t('searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Sort */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 pr-8 focus:outline-none focus:bg-white focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all"
              >
                <option value="name">{t('sortByName')}</option>
                <option value="price-high">{t('priceHighToLow')}</option>
                <option value="price-low">{t('priceLowToHigh')}</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <FiFilter className="text-gray-400" />
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-50 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm text-purple-600' : 'text-gray-600 hover:text-purple-600'} transition-all`}
              >
                <FiGrid size={20} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow-sm text-purple-600' : 'text-gray-600 hover:text-purple-600'} transition-all`}
              >
                <FiList size={20} />
              </button>
            </div>
          </div>
        </motion.div>

        {/* Designs Grid/List */}
        <AnimatePresence mode="wait">
          {filteredDesigns && filteredDesigns.length > 0 ? (
            <motion.div
              key={viewMode}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6' : 'space-y-4'}
            >
              {filteredDesigns.map((design, index) => {
                const designData: any = parseDesignJson(design.designJson)

                if (viewMode === 'grid') {
                  return (
                    <motion.div
                      key={design.id}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.05 }}
                      className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group cursor-pointer"
                      onClick={() => setSelectedDesign(design)}
                    >
                      {/* Thumbnail */}
                      <div className="relative h-48 bg-gradient-to-br from-purple-100 to-pink-100 overflow-hidden">
                        {/* Show furniture type specific preview */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <div className="w-20 h-20 mx-auto mb-3 bg-white/80 rounded-lg flex items-center justify-center">
                              <svg
                                viewBox="0 0 100 100"
                                className="w-16 h-16 text-purple-500"
                                fill="currentColor"
                              >
                                {(designData.model || designData.furnitureType || '').toLowerCase().includes('bergere') ? (
                                  // Bergere chair icon
                                  <path d="M20 75h60v5H20zm5-60h50c5 0 10 5 10 10v35c0 5-5 10-10 10H25c-5 0-10-5-10-10V25c0-5 5-10 10-10zm5 10v30h40V25H30zm-5 35h50v10H25V60z" />
                                ) : (
                                  // Armchair icon
                                  <path d="M25 20h50c8 0 15 7 15 15v25c0 8-7 15-15 15h-5v10h-5V75H35v10h-5V75h-5c-8 0-15-7-15-15V35c0-8 7-15 15-15zm0 10c-3 0-5 2-5 5v25c0 3 2 5 5 5h50c3 0 5-2 5-5V35c0-3-2-5-5-5H25zm10 10h30v20H35V40z" />
                                )}
                              </svg>
                            </div>
                            <p className="text-xs text-purple-600 font-semibold">
                              {designData.model || designData.furnitureType || 'Armchair'}
                            </p>
                            <p className="text-xs text-purple-400 mt-1">
                              {designData.sharedFabric?.option || designData.fabric?.type || 'Custom'}
                            </p>
                          </div>
                        </div>
                        {/* Commented out 3D preview to avoid performance issues with multiple instances
                        <Suspense fallback={
                          <div className="absolute inset-0 flex items-center justify-center">
                            <FiPackage className="text-purple-300" size={64} />
                          </div>
                        }>
                          {designData && (
                            <div className="w-full h-full">
                              <BabylonScene
                                design={{
                                  furnitureType: (designData.model || designData.furnitureType || designData.armchairType || 'armchair') as 'armchair' | 'bergere',
                                  frameColor: designData.woodVeneerFabric?.option || designData.woodVeneerFabric?.color || 'CevizAhsap',
                                  upholstery: designData.sharedFabric?.option || 'brown',
                                  legType: designData.legs || '1 Ahşap',
                                  cushionSize: '50*50',
                                  cushionFabric: designData.sharedFabric?.option || 'brown',
                                  backCushion: designData.backPillow || 'Silikon Elyaf',
                                  decorativeCushions: {
                                    size: '50*50',
                                    fabric: designData.sharedFabric?.option || 'brown',
                                    quantity: designData.cushions?.length || 1
                                  },
                                  addOns: [],
                                  lowerFrame: designData.baseFrame || '1',
                                  sharedFabric: designData.sharedFabric?.option || 'brown',
                                  legs: designData.legs ? designData.legs.split(' ')[0] : '1',
                                  legFabric: designData.legFabric?.option || designData.legFabric?.color || 'CevizAhsap',
                                  woodVeneer: designData.woodVeneer || '1',
                                  woodVeneerFabric: designData.woodVeneerFabric?.option || designData.woodVeneerFabric?.color || 'CevizAhsap',
                                  armrest: {
                                    main: designData.armrest?.main || '1',
                                    sub: designData.armrest?.sub ? (Array.isArray(designData.armrest.sub) ? designData.armrest.sub : [designData.armrest.sub]) : []
                                  },
                                  cushion: {
                                    option: '1'
                                  },
                                  seat: {
                                    type: designData.seat?.option || '1',
                                    color: designData.sharedFabric?.color || 'brown',
                                    options: {
                                      cektirme: false,
                                      tekParca: false,
                                      bombe: false,
                                      kirisiklik: false,
                                      kulak: false
                                    }
                                  },
                                  fabric: {
                                    type: designData.sharedFabric?.option || 'cartela1',
                                    color: designData.sharedFabric?.color || '1'
                                  }
                                }}
                                furnitureType={(designData.model || designData.furnitureType || designData.armchairType || 'armchair') as 'armchair' | 'bergere'}
                                getPartFilePath={(type: string, subType: string, name: string) => {
                                  if (!name || name === '') return ''
                                  switch (subType) {
                                    case 'lowerFrame':
                                      return `/assets/${type}/objects/AltKasa/${name}`
                                    case 'legs':
                                      if (/^\d+$/.test(name)) {
                                        return `/assets/${type}/objects/Ayaklar/${name} Ahşap`
                                      }
                                      return `/assets/${type}/objects/Ayaklar/${name}`
                                    case 'woodVeneer':
                                      return `/assets/${type}/objects/AhsapKasa/${name}`
                                    case 'armrest':
                                      if (name.includes(':')) {
                                        const [armBase, option] = name.split(':')
                                        return `/assets/${type}/objects/Kol/${armBase}/${option}`
                                      }
                                      return `/assets/${type}/objects/Kol/KOL ${name}/BOŞ`
                                    case 'seat':
                                      return `/assets/${type}/objects/Minder/Kulaksız/${name}/BOŞ`
                                    default:
                                      return ''
                                  }
                                }}
                                backCushion={designData.backPillow || 'Silikon Elyaf'}
                                decorativeCushions={{
                                  size: '50*50',
                                  fabric: designData.sharedFabric?.option || 'brown',
                                  quantity: designData.cushions?.length || 1
                                }}
                              />
                            </div>
                          )}
                        </Suspense>
                        */}
                        <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1">
                          <p className="text-sm font-semibold text-purple-600">{formatCurrency(design.totalPrice || designData.totalPrice || 0)}</p>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 mb-1 truncate">{design.name || designData.name}</h3>
                        <p className="text-sm text-gray-600 mb-3">{design.furnitureType || designData.model || designData.furnitureType || designData.armchairType || t('armchair')}</p>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1 mb-3">
                          {designData.sharedFabric && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                              {designData.sharedFabric.option}
                            </span>
                          )}
                          {(designData.model === 'bergere' || designData.armchairType === 'bergere') ? (
                            <>
                              {designData.skeleton && (
                                <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                                  {t('skeleton')} {designData.skeleton.type}
                                </span>
                              )}
                            </>
                          ) : (
                            <>
                              {designData.legs && (
                                <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                                  {designData.legs}
                                </span>
                              )}
                              {designData.armrest && (
                                <span className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full">
                                  {t('arm')} {designData.armrest.main}
                                </span>
                              )}
                            </>
                          )}
                        </div>


                        {/* Actions */}
                        <div className="flex gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleAddToCart(design)
                            }}
                            className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-3 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 flex items-center justify-center gap-2 text-sm font-medium"
                          >
                            <FiShoppingCart size={16} />
                            {t('addToCart')}
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteDesign(design.id)
                            }}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <FiTrash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )
                } else {
                  return (
                    <motion.div
                      key={design.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 cursor-pointer"
                      onClick={() => setSelectedDesign(design)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-4">
                            <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center">
                              <svg
                                viewBox="0 0 100 100"
                                className="w-10 h-10 text-purple-500"
                                fill="currentColor"
                              >
                                {(designData.model || designData.furnitureType || '').toLowerCase().includes('bergere') ? (
                                  // Bergere chair icon
                                  <path d="M20 75h60v5H20zm5-60h50c5 0 10 5 10 10v35c0 5-5 10-10 10H25c-5 0-10-5-10-10V25c0-5 5-10 10-10zm5 10v30h40V25H30zm-5 35h50v10H25V60z" />
                                ) : (
                                  // Armchair icon
                                  <path d="M25 20h50c8 0 15 7 15 15v25c0 8-7 15-15 15h-5v10h-5V75H35v10h-5V75h-5c-8 0-15-7-15-15V35c0-8 7-15 15-15zm0 10c-3 0-5 2-5 5v25c0 3 2 5 5 5h50c3 0 5-2 5-5V35c0-3-2-5-5-5H25zm10 10h30v20H35V40z" />
                                )}
                              </svg>
                              {/* Commented out 3D preview to avoid performance issues
                              <Suspense fallback={
                                <div className="w-full h-full flex items-center justify-center">
                                  <FiPackage className="text-purple-500" size={24} />
                                </div>
                              }>
                                {designData && (
                                  <div className="w-full h-full transform scale-[0.3] origin-center">
                                    <BabylonScene
                                      design={{
                                        furnitureType: designData.model || designData.furnitureType || designData.armchairType || 'armchair',
                                        frameColor: designData.woodVeneerFabric?.option || 'CevizAhsap',
                                        upholstery: designData.sharedFabric?.option || 'brown',
                                        legType: designData.legs || '1 Ahşap',
                                        cushionSize: '50*50',
                                        cushionFabric: designData.sharedFabric?.option || 'brown',
                                        backCushion: designData.backPillow || 'Silikon Elyaf',
                                        decorativeCushions: {
                                          size: '50*50',
                                          fabric: designData.sharedFabric?.option || 'brown',
                                          quantity: 1
                                        },
                                        addOns: [],
                                        lowerFrame: designData.baseFrame || '1',
                                        sharedFabric: designData.sharedFabric?.option || 'brown',
                                        legs: designData.legs ? designData.legs.split(' ')[0] : '1',
                                        legFabric: designData.legFabric?.option || 'CevizAhsap',
                                        woodVeneer: designData.woodVeneer || '1',
                                        woodVeneerFabric: designData.woodVeneerFabric?.option || 'CevizAhsap',
                                        armrest: {
                                          main: designData.armrest?.main || '1',
                                          sub: []
                                        },
                                        cushion: {
                                          option: '1'
                                        },
                                        seat: {
                                          type: '1',
                                          color: 'brown',
                                          options: {
                                            cektirme: false,
                                            tekParca: false,
                                            bombe: false,
                                            kirisiklik: false,
                                            kulak: false
                                          }
                                        },
                                        fabric: {
                                          type: designData.sharedFabric?.option || 'cartela1',
                                          color: designData.sharedFabric?.color || '1'
                                        }
                                      }}
                                      furnitureType={(designData.model || 'armchair') as 'armchair' | 'bergere'}
                                      getPartFilePath={(type: string, subType: string, name: string) => {
                                        if (!name) return ''
                                        return `/assets/${type}/objects/${subType}/${name}`
                                      }}
                                      backCushion={designData.backPillow || 'Silikon Elyaf'}
                                      decorativeCushions={{
                                        size: '50*50',
                                        fabric: designData.sharedFabric?.option || 'brown',
                                        quantity: 1
                                      }}
                                    />
                                  </div>
                                )}
                              </Suspense>
                              */}
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900">{design.name || designData.name}</h3>
                              <p className="text-sm text-gray-600">{design.furnitureType || designData.model || designData.furnitureType || designData.armchairType || t('armchair')}</p>
                              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                <span className="font-semibold text-purple-600">
                                  {formatCurrency(design.totalPrice || designData.totalPrice || 0)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleAddToCart(design)
                            }}
                            className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-2 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 flex items-center gap-2 text-sm font-medium"
                          >
                            <FiShoppingCart size={16} />
                            {t('addToCart')}
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteDesign(design.id)
                            }}
                            className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          >
                            <FiTrash2 size={18} />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )
                }
              })}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white rounded-2xl shadow-lg p-12 text-center"
            >
              <div className="flex flex-col items-center justify-center">
                <div className="mb-6 text-gray-400">
                  <FiHeart size={64} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {searchTerm ? t('noMatchingDesigns') : t('noSavedDesigns')}
                </h3>
                <p className="text-gray-600 mb-6 max-w-md">
                  {searchTerm ? t('tryAdjustingSearch') : t('startDesigningDescription')}
                </p>
                {!searchTerm && (
                  <button
                    onClick={() => navigate('/store/design')}
                    className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 flex items-center gap-2"
                  >
                    <FiEdit3 />
                    {t('startDesigning')}
                  </button>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Design Detail Modal */}
      <AnimatePresence>
        {selectedDesign && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
            onClick={() => setSelectedDesign(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                {(() => {
                  const designData: any = parseDesignJson(selectedDesign.designJson)
                  return (
                    <>
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-2xl font-bold text-gray-900">{selectedDesign.name || designData.name}</h2>
                        <button
                          onClick={() => setSelectedDesign(null)}
                          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                        >
                          <FiX size={24} />
                        </button>
                      </div>

                      {/* Preview */}
                      <div className="h-64 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl mb-6 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-32 h-32 mx-auto mb-4 bg-white/90 rounded-xl flex items-center justify-center">
                            <svg
                              viewBox="0 0 100 100"
                              className="w-24 h-24 text-purple-500"
                              fill="currentColor"
                            >
                              {(designData.model || designData.furnitureType || '').toLowerCase().includes('bergere') ? (
                                // Bergere chair icon
                                <path d="M20 75h60v5H20zm5-60h50c5 0 10 5 10 10v35c0 5-5 10-10 10H25c-5 0-10-5-10-10V25c0-5 5-10 10-10zm5 10v30h40V25H30zm-5 35h50v10H25V60z" />
                              ) : (
                                // Armchair icon
                                <path d="M25 20h50c8 0 15 7 15 15v25c0 8-7 15-15 15h-5v10h-5V75H35v10h-5V75h-5c-8 0-15-7-15-15V35c0-8 7-15 15-15zm0 10c-3 0-5 2-5 5v25c0 3 2 5 5 5h50c3 0 5-2 5-5V35c0-3-2-5-5-5H25zm10 10h30v20H35V40z" />
                              )}
                            </svg>
                          </div>
                          <p className="text-lg text-purple-600 font-semibold mb-2">{designData.model || designData.furnitureType || 'Armchair'}</p>
                          <p className="text-sm text-purple-400">{designData.sharedFabric?.option || designData.fabric?.type || 'Custom Design'}</p>
                        </div>
                        {/* Commented out 3D preview to avoid loading issues
                        <Suspense fallback={
                          <div className="h-full flex items-center justify-center">
                            <FiPackage className="text-purple-300" size={80} />
                          </div>
                        }>
                          <div className="w-full h-full">
                            <BabylonScene
                              design={{
                                furnitureType: designData.model || designData.furnitureType || designData.armchairType || 'armchair',
                                frameColor: designData.woodVeneerFabric?.option || designData.woodVeneerFabric?.color || 'CevizAhsap',
                                upholstery: designData.sharedFabric?.option || 'brown',
                                legType: designData.legs || '1 Ahşap',
                                cushionSize: '50*50',
                                cushionFabric: designData.sharedFabric?.option || 'brown',
                                backCushion: designData.backPillow || 'Silikon Elyaf',
                                decorativeCushions: {
                                  size: '50*50',
                                  fabric: designData.sharedFabric?.option || 'brown',
                                  quantity: designData.cushions?.length || 1
                                },
                                addOns: [],
                                lowerFrame: designData.baseFrame || '1',
                                sharedFabric: designData.sharedFabric?.option || 'brown',
                                legs: designData.legs ? designData.legs.split(' ')[0] : '1',
                                legFabric: designData.legFabric?.option || designData.legFabric?.color || 'CevizAhsap',
                                woodVeneer: designData.woodVeneer || '1',
                                woodVeneerFabric: designData.woodVeneerFabric?.option || designData.woodVeneerFabric?.color || 'CevizAhsap',
                                armrest: {
                                  main: designData.armrest?.main || '1',
                                  sub: designData.armrest?.sub ? (Array.isArray(designData.armrest.sub) ? designData.armrest.sub : [designData.armrest.sub]) : []
                                },
                                cushion: {
                                  option: '1'
                                },
                                seat: {
                                  type: designData.seat?.option || '1',
                                  color: designData.sharedFabric?.color || 'brown',
                                  options: {
                                    cektirme: false,
                                    tekParca: false,
                                    bombe: false,
                                    kirisiklik: false,
                                    kulak: false
                                  }
                                },
                                fabric: {
                                  type: designData.sharedFabric?.option || 'cartela1',
                                  color: designData.sharedFabric?.color || '1'
                                }
                              }}
                              furnitureType={(designData.model || designData.furnitureType || designData.armchairType || 'armchair') as 'armchair' | 'bergere'}
                              getPartFilePath={(type: string, subType: string, name: string) => {
                                if (!name || name === '') return ''
                                switch (subType) {
                                  case 'lowerFrame':
                                    return `/assets/${type}/objects/AltKasa/${name}`
                                  case 'legs':
                                    if (/^\d+$/.test(name)) {
                                      return `/assets/${type}/objects/Ayaklar/${name} Ahşap`
                                    }
                                    return `/assets/${type}/objects/Ayaklar/${name}`
                                  case 'woodVeneer':
                                    return `/assets/${type}/objects/AhsapKasa/${name}`
                                  case 'armrest':
                                    if (name.includes(':')) {
                                      const [armBase, option] = name.split(':')
                                      return `/assets/${type}/objects/Kol/${armBase}/${option}`
                                    }
                                    return `/assets/${type}/objects/Kol/KOL ${name}/BOŞ`
                                  case 'seat':
                                    return `/assets/${type}/objects/Minder/Kulaksız/${name}/BOŞ`
                                  default:
                                    return ''
                                }
                              }}
                              backCushion={designData.backPillow || 'Silikon Elyaf'}
                              decorativeCushions={{
                                size: '50*50',
                                fabric: designData.sharedFabric?.option || 'brown',
                                quantity: designData.cushions?.length || 1
                              }}
                            />
                          </div>
                        </Suspense>
                        */}
                      </div>

                      {/* Details */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <span className="font-medium text-gray-700">{t('type')}</span>
                          <span className="text-gray-900">{selectedDesign.furnitureType || designData.model || designData.furnitureType || designData.armchairType || t('armchair')}</span>
                        </div>

                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <span className="font-medium text-gray-700">{t('price')}</span>
                          <span className="text-2xl font-bold text-purple-600">{formatCurrency(selectedDesign.totalPrice || designData.totalPrice || 0)}</span>
                        </div>


                        {/* Components */}
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h3 className="font-medium text-gray-700 mb-3">{t('components')}</h3>
                          <div className="space-y-2">
                            {/* Check if it's bergere or armchair */}
                            {(designData.model === 'bergere' || designData.armchairType === 'bergere') ? (
                              <>
                                {/* Bergere components */}
                                {designData.sharedFabric && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('fabric')}</span>
                                    <span className="text-sm font-medium">
                                      {designData.sharedFabric.option} - {designData.sharedFabric.color}
                                    </span>
                                  </div>
                                )}
                                {(designData.skeleton || designData.woodVeneerFabric) && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('skeletonColor')}</span>
                                    <span className="text-sm font-medium">
                                      {designData.skeleton?.color || designData.woodVeneerFabric?.color || 'Ceviz'}
                                    </span>
                                  </div>
                                )}
                              </>
                            ) : (
                              <>
                                {/* Armchair components */}
                                {designData.sharedFabric && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('fabric')}</span>
                                    <span className="text-sm font-medium">
                                      {designData.sharedFabric.option} - {designData.sharedFabric.color}
                                    </span>
                                  </div>
                                )}
                                {designData.armrest && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('armrest')}</span>
                                    <span className="text-sm font-medium">
                                      {designData.armrest.main}{designData.armrest.sub && designData.armrest.sub !== 'BOŞ' ? ` - ${designData.armrest.sub}` : ''}
                                    </span>
                                  </div>
                                )}
                                {designData.legs && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('legs')}</span>
                                    <span className="text-sm font-medium">{designData.legs}</span>
                                  </div>
                                )}
                                {designData.seat && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('seat')}</span>
                                    <span className="text-sm font-medium">
                                      {designData.seat.option}{designData.seat.sub && designData.seat.sub !== 'BOŞ' ? ` - ${designData.seat.sub}` : ''}
                                    </span>
                                  </div>
                                )}
                                {designData.woodVeneerFabric && (
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600">{t('frameColor')}</span>
                                    <span className="text-sm font-medium">{designData.woodVeneerFabric.color}</span>
                                  </div>
                                )}
                              </>
                            )}

                            {/* Common components */}
                            {designData.backPillow && (
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">{t('backPillow')}</span>
                                <span className="text-sm font-medium">{designData.backPillow}</span>
                              </div>
                            )}
                            {designData.cushions && designData.cushions.length > 0 && (
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600">{t('cushions')}</span>
                                <span className="text-sm font-medium">{designData.cushions.length} {t('pieces')}</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-3 pt-4">
                          <button
                            onClick={() => {
                              handleAddToCart(selectedDesign)
                              setSelectedDesign(null)
                            }}
                            className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-300 flex items-center justify-center gap-2 font-medium"
                          >
                            <FiShoppingCart />
                            {t('addToCart')}
                          </button>
                          <button
                            onClick={() => {
                              const designData = parseDesignJson(selectedDesign.designJson)
                              navigate(`/store/design?edit=${selectedDesign.id}`, {
                                state: {
                                  savedDesign: {
                                    ...designData,
                                    id: selectedDesign.id,
                                    name: selectedDesign.name || designData.name
                                  }
                                }
                              })
                            }}
                            className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center gap-2 font-medium"
                          >
                            <FiEdit3 />
                            {t('editDesign')}
                          </button>
                        </div>
                      </div>
                    </>
                  )
                })()}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default SavedDesigns