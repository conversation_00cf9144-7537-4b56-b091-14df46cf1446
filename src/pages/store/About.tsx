import { FiMapPin, FiPhone, FiMail, FiClock, FiInstagram, FiFacebook, FiTwitter } from 'react-icons/fi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import { useTranslation } from 'react-i18next';

export default function About() {
  const { t } = useTranslation(['about']);
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="mb-16 text-center">
        <h1 className="text-4xl font-bold mb-4">Ka<PERSON>ul <PERSON>bilya</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          {t('heroText', 'We design and craft premium furniture with an emphasis on customization, comfort, and enduring quality.')}
        </p>
      </div>
      
      {/* Our Story */}
      <section className="mb-16">
        <Card className="overflow-hidden">
          <div className="grid md:grid-cols-2 gap-0">
            <div className="p-8">
              <CardHeader className="p-0 mb-4">
                <CardTitle className="text-3xl">{t('ourStory', 'Our Story')}</CardTitle>
              </CardHeader>
              <CardContent className="p-0 space-y-4">
                <p>
                  {t('storyPart1', 'Founded in 2010, Kapsul Mobilya began as a small workshop with a vision to combine traditional craftsmanship with modern design. Our founder, Ahmet Yılmaz, learned the art of furniture making from his father, continuing a family tradition spanning three generations.')}
                </p>
                <p>
                  {t('storyPart2', 'What started as a local business has now grown into a recognized brand, serving customers nationwide while maintaining the same attention to detail and quality that defined us from the beginning.')}
                </p>
                <p>
                  {t('storyPart3', 'Today, we continue to push the boundaries of furniture design, embracing new technologies while honoring the timeless techniques that form the foundation of our craft.')}
                </p>
              </CardContent>
            </div>
            <div className="bg-gray-100 flex items-center justify-center">
              <img 
                src="/about-workshop.jpg" 
                alt="Our workshop"
                className="object-cover w-full h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://images.unsplash.com/photo-1581539250439-c96689b516dd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80';
                }}
              />
            </div>
          </div>
        </Card>
      </section>
      
      {/* Our Values */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold mb-8 text-center">{t('ourValues', 'Our Values')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>{t('qualityTitle', 'Uncompromising Quality')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('qualityDescription', 'We select only the finest materials and employ rigorous quality control at every stage of production to ensure furniture that lasts for generations.')}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>{t('sustainabilityTitle', 'Sustainability')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('sustainabilityDescription', 'We source materials responsibly, minimize waste in our production process, and design furniture that stands the test of time rather than following short-lived trends.')}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>{t('customizationTitle', 'Personalized Design')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('customizationDescription', 'We believe furniture should reflect your personal style and needs. Our customization options allow you to create pieces that are uniquely yours.')}</p>
            </CardContent>
          </Card>
        </div>
      </section>
      
      {/* Our Process */}
      <section className="mb-16">
        <h2 className="text-3xl font-bold mb-8 text-center">{t('ourProcess', 'Our Process')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <Card>
            <CardHeader>
              <div className="bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center mb-4">
                <span className="font-bold">1</span>
              </div>
              <CardTitle>{t('designTitle', 'Design')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('designDescription', 'Your furniture journey begins with design. Use our online customizer or work with our design team to create your perfect piece.')}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center mb-4">
                <span className="font-bold">2</span>
              </div>
              <CardTitle>{t('materialTitle', 'Material Selection')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('materialDescription', 'Choose from our curated selection of premium woods, fabrics, and finishes to match your aesthetic and functional needs.')}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center mb-4">
                <span className="font-bold">3</span>
              </div>
              <CardTitle>{t('craftingTitle', 'Crafting')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('craftingDescription', 'Our skilled artisans build your furniture with precision and care, combining traditional techniques with modern technology.')}</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <div className="bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center mb-4">
                <span className="font-bold">4</span>
              </div>
              <CardTitle>{t('deliveryTitle', 'Delivery')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{t('deliveryDescription', 'Your finished piece is carefully packaged and delivered to your home, where it will become part of your life story.')}</p>
            </CardContent>
          </Card>
        </div>
      </section>
      
      {/* Contact Information */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl">{t('contactUs', 'Contact Us')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <FiMapPin className="mt-1 text-primary" size={20} />
                  <div>
                    <h3 className="font-medium">{t('address', 'Address')}</h3>
                    <p className="text-gray-600">
                      Mobilya Caddesi No:123<br />
                      Masko Sanayi Sitesi<br />
                      34000 İstanbul, Türkiye
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <FiPhone className="mt-1 text-primary" size={20} />
                  <div>
                    <h3 className="font-medium">{t('phone', 'Phone')}</h3>
                    <p className="text-gray-600">+90 212 123 45 67</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <FiMail className="mt-1 text-primary" size={20} />
                  <div>
                    <h3 className="font-medium">{t('email', 'Email')}</h3>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <FiClock className="mt-1 text-primary" size={20} />
                  <div>
                    <h3 className="font-medium">{t('hours', 'Business Hours')}</h3>
                    <p className="text-gray-600">
                      {t('weekdays', 'Monday - Friday')}: 9:00 - 18:00<br />
                      {t('saturday', 'Saturday')}: 10:00 - 16:00<br />
                      {t('sunday', 'Sunday')}: {t('closed', 'Closed')}
                    </p>
                  </div>
                </div>
                <div className="pt-4">
                  <h3 className="font-medium mb-2">{t('followUs', 'Follow Us')}</h3>
                  <div className="flex space-x-4">
                    <a href="#" className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors" aria-label="Instagram">
                      <FiInstagram size={20} />
                    </a>
                    <a href="#" className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors" aria-label="Facebook">
                      <FiFacebook size={20} />
                    </a>
                    <a href="#" className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition-colors" aria-label="Twitter">
                      <FiTwitter size={20} />
                    </a>
                  </div>
                </div>
              </div>
              <div className="w-full h-80 bg-gray-200 rounded-md">
                {/* This would be replaced with an actual map component in a real implementation */}
                <div className="w-full h-full flex items-center justify-center text-gray-500">
                  {t('mapPlaceholder', 'Map would be displayed here')}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
