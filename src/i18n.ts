import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

// Function to import all JSON files from a directory
async function importLocaleFiles(lang: string) {
  const modules = import.meta.glob('/public/locales/**/*.json', { eager: true });
  const resources: Record<string, Record<string, unknown>> = {};

  for (const path in modules) {
    if (path.includes(`/${lang}/`)) {
      const namespace = path.match(/\/([^/]+)\.json$/)?.[1];
      if (namespace && typeof modules[path] === 'object' && modules[path] !== null) {
        resources[namespace] = modules[path] as Record<string, unknown>;
      }
    }
  }

  return resources;
}

// Initialize i18n immediately
(async function() {
  try {
    const enResources = await importLocaleFiles('en');
    const trResources = await importLocaleFiles('tr');

    const resources: {
      [key: string]: {
        [namespace: string]: Record<string, unknown>;
      };
    } = {
      en: enResources,
      tr: trResources,
    };

    const namespaces = Object.keys(enResources).length > 0 ? Object.keys(enResources) : ['common'];

    i18n
      .use(Backend)
      .use(LanguageDetector)
      .use(initReactI18next)
      .init({
        fallbackLng: 'tr',
        lng: 'tr',
        debug: false,
        interpolation: {
          escapeValue: false,
        },
        resources,
        react: {
          useSuspense: false,
        },
        ns: namespaces,
        defaultNS: namespaces[0] || 'common',
        backend: {
          loadPath: '/locales/{{lng}}/{{ns}}.json',
        },
      });
  } catch (error) {
    console.error('Error initializing i18n:', error);
  }
})();

export default i18n;