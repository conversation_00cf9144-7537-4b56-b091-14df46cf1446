import React, { useEffect } from 'react'
import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
  useLocation,
} from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { useAuth } from '@/hooks/useAuth'
import Checkout from '@/pages/store/Checkout'
import { AuthForm } from '@/pages/auth/AuthForm'
import { Header } from '@/components/layout/Header'
import { AdminLayout } from '@/components/layout/AdminLayout'
import { ShoppingCart } from '@/components/store/ShoppingCart'
import FurnitureCustomizer from './pages/store/FurnitureCustomizer'
import OrderTracking from './pages/store/OrderTracking'
import StoreSettings from './pages/store/Settings'
import CostManagement from './pages/admin/CostManagement'
import UserManagement from './pages/admin/UserManagement'
import AdminDashboard from './pages/admin/AdminDashboard'
import NotFoundPage from './pages/NotFoundPage'
// Page title management will be handled directly in components
import LandingPage from './pages/LandingPage'
import OrderConfirmation from './pages/store/OrderConfirmation'
import SavedDesigns from './pages/store/SavedDesigns'

// Create QueryClient instance for React Query
const queryClient = new QueryClient()

// Protected route component for authenticated routes
function ProtectedRoute({
  children,
  allowedRoles,
}: {
  children: React.ReactNode
  allowedRoles?: string[]
}) {
  const { user, loading, initialized } = useAuth()
  const location = useLocation()

  // Show loading spinner only during initial load
  if (!initialized || loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-100">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Redirect if not authenticated
  if (!user) {
    return <Navigate to="/auth" replace state={{ from: location }} />
  }

  // Check role permissions
  if (allowedRoles && !allowedRoles.includes(user.role)) {
    return <Navigate to="/" replace state={{ from: location }} />
  }

  return <>{children}</>
}

// Layout component for the application
function AppLayout({
  children,
  shouldShowHeader,
  onCartClick,
  isCartOpen,
  onCartClose
}: {
  children: React.ReactNode
  shouldShowHeader: boolean
  onCartClick: () => void
  isCartOpen: boolean
  onCartClose: () => void
}) {
  return (
    <div className="flex flex-col min-h-screen">
      {shouldShowHeader && (
        <>
          <Header onCartClick={onCartClick} />
          <ShoppingCart isOpen={isCartOpen} onClose={onCartClose} />
        </>
      )}
      <main className="flex-grow">{children}</main>
    </div>
  )
}

// Main content with routes and logic
function AppContent() {
  const { user, login, checkAuth } = useAuth()
  const [isCartOpen, setIsCartOpen] = React.useState(false)
  const location = useLocation()

  // Create an adapter for the login function to match what AuthForm expects
  const handleLogin = async (data: { username: string; password: string }) => {
    return await login(data.username, data.password)
  }

  // Hide header on auth page and admin pages
  const hideHeaderPaths = ["/auth"]
  const shouldShowHeader = !hideHeaderPaths.includes(location.pathname) && !location.pathname.startsWith('/admin')

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  return (
    <AppLayout
      shouldShowHeader={shouldShowHeader}
      onCartClick={() => setIsCartOpen(true)}
      isCartOpen={isCartOpen}
      onCartClose={() => setIsCartOpen(false)}
    >
      <Routes>
        <Route
          path="/dashboard"
          element={
            !user ? (
              <Navigate to="/" replace />
            ) : user.role === 'admin' ? (
              <Navigate to="/admin" replace />
            ) : (
              <Navigate to="/store/design" replace />
            )
          }
        />
        <Route
          path="/store"
          element={
            <ProtectedRoute>
              <OrderTracking />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/orders"
          element={
            <ProtectedRoute>
              <OrderTracking />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/design"
          element={
            <ProtectedRoute>
              <FurnitureCustomizer />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/checkout"
          element={
            <ProtectedRoute>
              <Checkout />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/settings"
          element={
            <ProtectedRoute>
              <StoreSettings />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/order-confirmation"
          element={
            <ProtectedRoute>
              <OrderConfirmation />
            </ProtectedRoute>
          }
        />
        <Route
          path="/store/saved-designs"
          element={
            <ProtectedRoute>
              <SavedDesigns />
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin"
          element={
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<AdminDashboard />} />
          <Route path="user-management" element={<UserManagement />} />
          <Route path="cost-management" element={<CostManagement />} />
        </Route>
        <Route
          path="/auth"
          element={
            user ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <AuthForm onLogin={handleLogin} />
            )
          }
        />
        <Route path="/" element={<LandingPage />} />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </AppLayout>
  )
}

// Main App component
function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <AppContent />
      </Router>
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss={false}
        draggable
        pauseOnHover={false}
        theme="light"
        style={{ zIndex: 9999 }}
        toastStyle={{
          backgroundColor: '#fff',
          color: '#374151'
        }}
        aria-label={undefined}
      />
    </QueryClientProvider>
  )
}

export default App
