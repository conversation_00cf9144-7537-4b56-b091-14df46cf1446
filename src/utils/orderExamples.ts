// Example usage of enhanced order creation with complete data

import { backendService, CreateOrderPayload } from '@/services/api'
import { EnhancedOrder } from '@/types'

/**
 * Example: Create order with complete customer and item data
 */
export const createOrderExample = async () => {
  const orderData: CreateOrderPayload = {
    // Customer information
    customerInfo: {
      name: "<PERSON>",
      surname: "<PERSON><PERSON>", 
      email: "<EMAIL>",
      phone: "+1234567890",
      address: "123 Main St, City, Country"
    },

    // Order items with complete pricing
    items: [
      {
        design: {
          legs: "1 Ahşap",
          baseFrame: "1",
          woodVeneer: "1",
          armrest: {
            main: "KOL 1",
            sub: "BOŞ",
            mainAhsap: false
          },
          seat: {
            option: "1",
            sub: "BOŞ"
          },
          sharedFabric: {
            option: "cartela1",
            color: "1"
          },
          woodVeneerFabric: {
            option: "1",
            color: "CevizAhsap"
          },
          legFabric: {
            option: "1 Ahşap",
            color: "CevizAhsap"
          },
          mainAhsapFabric: {
            option: "1",
            color: "CevizAhsap"
          },
          model: "armchair",
          armchairType: "premium",
          backPillow: "Silikon Elyaf",
          cushions: [
            {
              cushionType: "small",
              fabric: "cartela1",
              color: "blue"
            }
          ],
          customerName: "John",
          customerSurname: "Doe",
          totalPrice: 1250.00,
          barcode: "ARM-001-2024"
        },
        quantity: 2,
        unitPrice: 1250.00,
        totalPrice: 2500.00
      },
      {
        design: {
          legs: "2 Metal",
          baseFrame: "2",
          woodVeneer: "2",
          armrest: {
            main: "KOL 2",
            sub: "BOŞ",
            mainAhsap: false
          },
          seat: {
            option: "2",
            sub: "BOŞ"
          },
          sharedFabric: {
            option: "cartela2",
            color: "red"
          },
          woodVeneerFabric: {
            option: "2",
            color: "MeseAhsap"
          },
          legFabric: {
            option: "2 Metal",
            color: "Black"
          },
          mainAhsapFabric: {
            option: "2",
            color: "MeseAhsap"
          },
          model: "bergere",
          armchairType: "classic",
          backPillow: "Foam",
          cushions: [],
          customerName: "John",
          customerSurname: "Doe",
          totalPrice: 980.00,
          barcode: "BER-002-2024"
        },
        quantity: 1,
        unitPrice: 980.00,
        totalPrice: 980.00
      }
    ],

    // Order totals
    subtotal: 3480.00,
    tax: 626.40, // 18% tax
    orderTotal: 4106.40,

    // Additional order information
    notes: "Customer requested expedited delivery",
    deliveryDate: "2024-02-15",
    
    // Flag to use direct data instead of cart
    useDirectData: true
  }

  try {
    // Method 1: Use enhanced order creation
    const result = await backendService.createOrderWithData(orderData)
    console.log('Order created successfully:', result)
    return result

  } catch (error) {
    console.error('Failed to create order:', error)
    throw error
  }
}

/**
 * Example: Create order bypassing cart system entirely
 */
export const createDirectOrderExample = async () => {
  const orderData: CreateOrderPayload = {
    customerInfo: {
      name: "Jane",
      surname: "Smith"
    },
    items: [
      {
        design: {
          legs: "1 Ahşap",
          baseFrame: "1",
          woodVeneer: "1",
          armrest: { main: "KOL 1", sub: "BOŞ", mainAhsap: false },
          seat: { option: "1", sub: "BOŞ" },
          sharedFabric: { option: "cartela1", color: "1" },
          woodVeneerFabric: { option: "1", color: "CevizAhsap" },
          legFabric: { option: "1 Ahşap", color: "CevizAhsap" },
          mainAhsapFabric: { option: "1", color: "CevizAhsap" },
          model: "armchair",
          customerName: "Jane",
          customerSurname: "Smith",
          totalPrice: 1200.00,
          barcode: "DIRECT-001"
        },
        quantity: 1,
        unitPrice: 1200.00,
        totalPrice: 1200.00
      }
    ],
    orderTotal: 1200.00
  }

  try {
    // Method 2: Use direct order creation (bypasses cart completely)
    const result = await backendService.createDirectOrder(orderData)
    console.log('Direct order created successfully:', result)
    return result

  } catch (error) {
    console.error('Failed to create direct order:', error)
    throw error
  }
}

/**
 * Backward compatibility: Use existing cart-based flow
 */
export const createOrderFromCartExample = async () => {
  try {
    // Method 3: Use existing cart-based flow (backward compatible)
    const result = await backendService.createOrder({})
    console.log('Cart-based order created successfully:', result)
    return result

  } catch (error) {
    console.error('Failed to create cart-based order:', error)
    throw error
  }
}