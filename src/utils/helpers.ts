export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

export function calculateTotalPrice(products: { price: number; quantity: number }[]): number {
  return products.reduce((total, product) => total + product.price * product.quantity, 0)
}

export function cn(...classes: string[]) {
  return classes.filter(Boolean).join(' ')
}