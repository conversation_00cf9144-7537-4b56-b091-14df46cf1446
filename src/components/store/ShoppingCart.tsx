import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  FiX, FiPlus, FiMinus, FiShoppingBag, FiTrash2,
  FiCheck, FiArrowRight, FiPackage, FiChevronDown,
  FiChevronUp, FiInfo, FiStar, FiDollarSign
} from 'react-icons/fi'
import { useCart } from '@/hooks/useCart'
import { useTranslation } from 'react-i18next'

type ShoppingCartProps = {
  isOpen: boolean
  onClose: () => void
}

export function ShoppingCart({ isOpen, onClose }: ShoppingCartProps) {
  const navigate = useNavigate()
  const { cartItems, removeFromCart, updateQuantity, fetchCart } = useCart()
  const [isLoading, setIsLoading] = useState(false)
  const [removingItem, setRemovingItem] = useState<string | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const { t } = useTranslation('shoppingCart')

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true)
      fetchCart().finally(() => setIsLoading(false))
    }
  }, [isOpen, fetchCart])

  // Calculate total price
  const calculateTotal = () => {
    return cartItems.reduce((total, item) => {
      return total + (item.design?.totalPrice || 0) * item.quantity
    }, 0)
  }

  const handleRemoveItem = async (itemId: string) => {
    if (itemId) {
      setRemovingItem(itemId)
      await removeFromCart(itemId)
      setRemovingItem(null)
    }
  }

  const handleUpdateQuantity = async (itemId: string, quantity: number) => {
    if (quantity < 1 || !itemId) return
    await updateQuantity(itemId, quantity)
  }

  const handleCheckout = () => {
    onClose()
    navigate('/store/checkout')
  }

  const toggleItemExpand = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  // Format fabric display with proper text formatting
  const formatFabricDisplay = (fabric: { option: string; color: string }) => {
    if (!fabric) return '-'

    // Split camelCase or PascalCase into readable text
    const formatText = (text: string) => {
      return text
        .replace(/([A-Z])/g, ' $1')
        .replace(/([0-9]+)/g, ' $1')
        .trim()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ')
    }

    return `${formatText(fabric.option)} - ${formatText(fabric.color)}`
  }

  if (!isOpen) return null

  return (
    <>
      {/* Enhanced overlay with blur effect */}
      <div
        className={`fixed inset-0 z-40 transition-all duration-500 bg-black/70 backdrop-blur-md`}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Enhanced shopping cart panel */}
      <div
        className="fixed inset-y-0 right-0 z-50 w-full max-w-3xl shadow-2xl"
        style={{
          willChange: 'transform',
          transform: isOpen ? 'none' : 'translateX(100%)',
          transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
      >
        <div className="bg-gradient-to-b from-gray-50 via-white to-gray-50 h-full flex flex-col">
          {/* Enhanced Header */}
          <div className="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-6 shadow-xl">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative flex justify-between items-start">
              <div>
                <h2 className="text-3xl font-bold flex items-center gap-3 mb-2">
                  <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                    <FiShoppingBag className="w-7 h-7" />
                  </div>
                  {t('shoppingCart')}
                </h2>
                <p className="text-purple-100 text-lg flex items-center gap-2">
                  <span className="px-3 py-1 bg-white/20 rounded-full text-sm font-semibold">
                    {cartItems.length}
                  </span>
                  {cartItems.length === 1 ? t('item', { defaultValue: 'item' }) : t('items', { defaultValue: 'items' })}
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-3 hover:bg-white/20 rounded-xl transition-all duration-300 transform hover:scale-110"
              >
                <FiX className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Cart Items with enhanced design */}
          <div className="flex-grow overflow-y-auto p-6 custom-scrollbar">
            {isLoading ? (
              <div className="flex flex-col justify-center items-center h-full">
                <div className="relative">
                  <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-200"></div>
                  <div className="animate-spin rounded-full h-20 w-20 border-4 border-purple-600 border-t-transparent absolute top-0 left-0"></div>
                </div>
                <p className="mt-6 text-gray-600 text-lg">{t('loadingCart', { defaultValue: 'Loading your items...' })}</p>
              </div>
            ) : cartItems && cartItems.length > 0 ? (
              <div className="space-y-6">
                {cartItems.map((item: any, index) => (
                  <div
                    key={item.id || index}
                    className={`bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 ${removingItem === item.id ? 'opacity-50 scale-95' : ''
                      }`}
                  >
                    {/* Enhanced Product Header */}
                    <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-6 border-b border-gray-200">
                      <div className="flex items-center gap-4">
                        {/* Product Image with gradient background */}
                        <div className="relative">
                          <div className="w-28 h-28 bg-gradient-to-br from-indigo-400 via-purple-400 to-pink-400 rounded-2xl flex items-center justify-center shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                            <FiPackage className="w-14 h-14 text-white" />
                          </div>
                          <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-1">
                            <FiCheck className="w-4 h-4" />
                          </div>
                        </div>

                        {/* Product Info */}
                        <div className="flex-grow">
                          <h3 className="font-bold text-2xl text-gray-900 mb-1">
                            {t(item.design.model)}
                          </h3>
                          <div className="flex items-center gap-4 text-sm">
                            <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full font-medium">
                              {t(item.design?.armchairType || 'armchairType')}
                            </span>
                            <span className="text-gray-600">
                              {t('barcode')}: <span className="font-mono font-semibold">{item.design?.barcode || 'N/A'}</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="flex items-center gap-2 text-sm bg-gray-100 px-3 py-1 rounded-lg">
                              <FiInfo className="text-gray-500" />
                              <span className="text-gray-600">{t('customer')}: </span>
                              <span className="font-semibold text-gray-800">
                                {item.design?.customerName} {item.design?.customerSurname}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Price Display */}
                        <div className="text-right">
                          <div className="bg-gradient-to-r from-indigo-500 to-purple-500 text-white px-4 py-2 rounded-xl">
                            <p className="text-2xl font-bold">
                              ${(item.design?.totalPrice || 0).toLocaleString('en-US', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              })}
                            </p>
                            <p className="text-xs opacity-90">{t('perItem')}</p>
                          </div>
                        </div>
                      </div>

                      {/* Toggle Details Button */}
                      <button
                        onClick={() => toggleItemExpand(item.id || `item-${index}`)}
                        className="mt-4 w-full py-2 px-4 bg-gradient-to-r from-purple-100 to-pink-100 hover:from-purple-200 hover:to-pink-200 text-purple-700 rounded-lg font-medium flex items-center justify-center gap-2 transition-all duration-300"
                      >
                        {expandedItems.has(item.id || `item-${index}`) ? (
                          <>
                            <FiChevronUp className="w-5 h-5" />
                            {t('hideDetails', { defaultValue: 'Hide Details' })}
                          </>
                        ) : (
                          <>
                            <FiChevronDown className="w-5 h-5" />
                            {t('showDetails', { defaultValue: 'Show Details' })}
                          </>
                        )}
                      </button>
                    </div>

                    {/* Configuration Details - Toggleable */}
                    {expandedItems.has(item.id || `item-${index}`) && (
                      <div className="p-6 bg-gradient-to-b from-white to-gray-50">
                        <h4 className="font-bold text-lg text-gray-900 mb-4 flex items-center gap-2">
                          <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
                          {t('configurationDetails', { defaultValue: 'Configuration Details' })}
                        </h4>

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Check if it's a bergere */}
                          {(item.design?.model === 'bergere' || item.design?.armchairType === 'bergere') ? (
                            <>
                              {/* Bergere-specific Details */}
                              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                                <h5 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-500 text-white rounded-lg flex items-center justify-center">
                                    <FiPackage className="w-4 h-4" />
                                  </div>
                                  {t('bergereDetails', { defaultValue: 'Bergere Details' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('skeletonType', { defaultValue: 'Skeleton Type' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.skeleton?.type || '1'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('skeletonColor', { defaultValue: 'Skeleton Color' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.skeleton?.color || item.design?.woodVeneerFabric?.color || 'Ceviz'}</span>
                                  </div>
                                  {item.design?.backPillow && (
                                    <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                      <span className="text-gray-700 font-medium">{t('backPillow', { defaultValue: 'Back Pillow' })}:</span>
                                      <span className="font-semibold text-gray-900">{item.design?.backPillow}</span>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Fabric Details for Bergere */}
                              <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200">
                                <h5 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-purple-500 text-white rounded-lg flex items-center justify-center">
                                    <FiStar className="w-4 h-4" />
                                  </div>
                                  {t('fabricDetails', { defaultValue: 'Fabrics' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('fabric', { defaultValue: 'Fabric' })}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.sharedFabric)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </>
                          ) : (
                            <>
                              {/* Armchair-specific Details */}
                              <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200">
                                <h5 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-500 text-white rounded-lg flex items-center justify-center">
                                    <FiPackage className="w-4 h-4" />
                                  </div>
                                  {t('structure', { defaultValue: 'Structure' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('legs', { defaultValue: 'Legs' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.legs || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('baseFrame', { defaultValue: 'Base Frame' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.baseFrame || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('woodVeneer', { defaultValue: 'Wood Veneer' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.woodVeneer || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('backPillow', { defaultValue: 'Back Pillow' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.backPillow || '-'}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Fabric Details Card */}
                              <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200">
                                <h5 className="font-semibold text-purple-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-purple-500 text-white rounded-lg flex items-center justify-center">
                                    <FiStar className="w-4 h-4" />
                                  </div>
                                  {t('fabricDetails', { defaultValue: 'Fabrics' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('sharedFabric', { defaultValue: 'Shared' })}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.sharedFabric)}
                                    </span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('woodVeneerFabric', { defaultValue: 'Wood Veneer' })}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.woodVeneerFabric)}
                                    </span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('legFabric', { defaultValue: 'Leg' })}:</span>
                                    <span className="font-semibold text-gray-900 text-right text-sm">
                                      {formatFabricDisplay(item.design?.legFabric)}
                                    </span>
                                  </div>
                                  {item.design?.armrest?.mainAhsap && (
                                    <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                      <span className="text-gray-700 font-medium">{t('mainAhsapFabric', { defaultValue: 'Main Ahşap' })}:</span>
                                      <span className="font-semibold text-gray-900 text-right text-sm">
                                        {formatFabricDisplay(item.design?.mainAhsapFabric)}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </>
                          )}

                          {/* Common Details for both types */}
                          {(item.design?.model !== 'bergere' && item.design?.armchairType !== 'bergere') && (
                            <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                              <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200">
                                <h5 className="font-semibold text-green-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-green-500 text-white rounded-lg flex items-center justify-center">
                                    <FiCheck className="w-4 h-4" />
                                  </div>
                                  {t('armrest', { defaultValue: 'Armrest' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('main', { defaultValue: 'Main' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.armrest?.main || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('sub', { defaultValue: 'Sub' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.armrest?.sub || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('mainAhsap', { defaultValue: 'Main Ahşap' })}:</span>
                                    <span className="font-semibold text-gray-900">
                                      {item.design?.armrest?.mainAhsap ? (
                                        <span className="flex items-center gap-1 text-green-600">
                                          <FiCheck className="w-4 h-4" /> {t('yes', { defaultValue: 'Yes' })}
                                        </span>
                                      ) : (
                                        <span className="text-gray-500">{t('no', { defaultValue: 'No' })}</span>
                                      )}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-4 rounded-xl border border-orange-200">
                                <h5 className="font-semibold text-orange-900 mb-3 flex items-center gap-2">
                                  <div className="w-8 h-8 bg-orange-500 text-white rounded-lg flex items-center justify-center">
                                    <FiDollarSign className="w-4 h-4" />
                                  </div>
                                  {t('seat', { defaultValue: 'Seat' })}
                                </h5>
                                <div className="space-y-2">
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('option', { defaultValue: 'Option' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.seat?.option || '-'}</span>
                                  </div>
                                  <div className="flex justify-between items-center p-2 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('sub', { defaultValue: 'Sub' })}:</span>
                                    <span className="font-semibold text-gray-900">{item.design?.seat?.sub || '-'}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Cushions if available */}
                          {item.design?.cushions && item.design.cushions.length > 0 && (
                            <div className="lg:col-span-2 bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-xl border border-teal-200">
                              <h5 className="font-semibold text-teal-900 mb-3 flex items-center gap-2">
                                <div className="w-8 h-8 bg-teal-500 text-white rounded-lg flex items-center justify-center">
                                  <FiStar className="w-4 h-4" />
                                </div>
                                {t('cushions', { defaultValue: 'Cushions' })} ({item.design.cushions.length})
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {item.design.cushions.map((cushion: any, cushionIndex: number) => (
                                  <div key={cushionIndex} className="flex items-center justify-between p-3 bg-white/70 rounded-lg">
                                    <span className="text-gray-700 font-medium">{t('cushion', { defaultValue: 'Cushion' })} {cushionIndex + 1}:</span>
                                    <span className="font-semibold text-gray-900 text-sm">
                                      {cushion.cushionType} • {cushion.fabric} • {cushion.color}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Quantity and Actions */}
                    <div className="p-6 bg-gray-50 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <span className="text-gray-700 font-medium">{t('quantity')}:</span>
                          <div className="flex items-center gap-2 bg-white rounded-xl p-1 shadow-md">
                            <button
                              className="p-2 hover:bg-gray-100 rounded-lg transition-all duration-200 transform hover:scale-110"
                              onClick={() => item.id && handleUpdateQuantity(item.id, item.quantity - 1)}
                            >
                              <FiMinus className="w-5 h-5 text-gray-600" />
                            </button>
                            <span className="w-16 text-center font-bold text-xl text-gray-900">{item.quantity}</span>
                            <button
                              className="p-2 hover:bg-gray-100 rounded-lg transition-all duration-200 transform hover:scale-110"
                              onClick={() => item.id && handleUpdateQuantity(item.id, item.quantity + 1)}
                            >
                              <FiPlus className="w-5 h-5 text-gray-600" />
                            </button>
                          </div>
                        </div>

                        <div className="flex items-center gap-6">
                          <div className="text-right">
                            <p className="text-sm text-gray-600">{t('subtotal', { defaultValue: 'Subtotal' })}</p>
                            <p className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                              ${((item.design?.totalPrice || 0) * item.quantity).toLocaleString('en-US', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                              })}
                            </p>
                          </div>
                          <button
                            onClick={() => item.id && handleRemoveItem(item.id)}
                            className="p-3 text-red-500 hover:text-white hover:bg-red-500 rounded-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12"
                            disabled={removingItem === item.id}
                          >
                            <FiTrash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <div className="relative">
                  <div className="w-40 h-40 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                    <FiShoppingBag className="w-20 h-20 text-gray-300" />
                  </div>
                  <div className="absolute -bottom-4 -right-4 bg-gray-300 text-white rounded-full p-3">
                    <FiX className="w-6 h-6" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-gray-700 mb-2 mt-8">{t('emptyCart')}</h3>
                <p className="text-gray-500 text-lg">{t('startAddingItems', { defaultValue: 'Start adding items to see them here' })}</p>
              </div>
            )}
          </div>

          {/* Enhanced Footer with Total and Complete Order */}
          {cartItems && cartItems.length > 0 && (
            <div className="bg-gradient-to-b from-white via-gray-50 to-white border-t-2 border-gray-200 p-6 shadow-2xl">
              <div className="space-y-6">
                {/* Order Summary */}
                <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-200">
                  <div className="space-y-3">
                    <div className="flex justify-between text-gray-700">
                      <span className="font-medium">
                        {t('total')} ({cartItems.length} {cartItems.length === 1 ? t('item', { defaultValue: 'item' }) : t('items', { defaultValue: 'items' })})
                      </span>
                      <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent">
                        ${calculateTotal().toLocaleString('en-US', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2
                        })}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Complete Order Button */}
                <button
                  onClick={handleCheckout}
                  className="w-full bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white font-bold py-5 px-8 rounded-2xl hover:from-purple-700 hover:via-pink-700 hover:to-red-700 transform hover:scale-[1.02] transition-all duration-300 shadow-2xl hover:shadow-3xl flex items-center justify-center gap-4 group relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                  <FiShoppingBag className="w-6 h-6 transform group-hover:scale-110 transition-transform" />
                  <span className="text-xl">{t('proceedToCheckout')}</span>
                  <FiArrowRight className="w-6 h-6 transform group-hover:translate-x-2 transition-transform" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #8b5cf6, #ec4899);
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #7c3aed, #db2777);
        }
      `}</style>
    </>
  )
}