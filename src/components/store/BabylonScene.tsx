import { useEffect, useRef } from 'react';
import * as BABYLON from '@babylonjs/core';
import '@babylonjs/loaders';

interface Design {
  furnitureType: 'armchair' | 'bergere';
  frameColor: string;
  upholstery: string;
  legType: string;
  cushionSize: string;
  cushionFabric: string;
  backCushion: string;
  decorativeCushions: {
    size: string;
    fabric: string;
    quantity: number;
  };
  addOns: string[];

  // Additional properties used in BabylonScene
  lowerFrame: string;
  sharedFabric: string;
  legs: string;
  legFabric: string;
  woodVeneer: string;
  woodVeneerFabric: string;
  armrest: {
    main: string;
    sub: string[];
  };
  cushion: {
    option: string;
  };
  // Seat options
  seat: {
    type: string;
    color: string;
    options: {
      cektirme: boolean;
      tekParca: boolean;
      bombe: boolean;
      kirisiklik: boolean;
      kulak: boolean;
    };
  };
  // Skeleton options for bergere
  skeleton?: {
    type: string;
    color: string;
  };
  // Fabric options
  fabric: {
    type: string;  // e.g., 'brown', 'grey', 'cartela1'
    color: string; // e.g., '1', '2', '3', etc.
  };
}

interface BabylonSceneProps {
  design: Design;
  furnitureType: string;
  getPartFilePath: (type: string, partType: string, name: string) => string;
  backCushion: string;
  decorativeCushions: {
    size: string;
    fabric: string;
    quantity: number;
  };
}

export default function BabylonScene({ design, furnitureType, getPartFilePath, backCushion, decorativeCushions }: BabylonSceneProps) {
  // Component props and state management
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const shadowGenerator = useRef<BABYLON.ShadowGenerator | null>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Create engine with antialias and adaptive sampling for better performance
    const engine = new BABYLON.Engine(canvasRef.current, true, {
      preserveDrawingBuffer: true,
      stencil: true,
      adaptToDeviceRatio: true
    });

    // Create scene with optimized settings
    const scene = new BABYLON.Scene(engine);
    scene.clearColor = new BABYLON.Color4(0.95, 0.95, 0.95, 1); // Light background
    scene.ambientColor = new BABYLON.Color3(0.5, 0.5, 0.5); // Moderate ambient light to allow for shadows

    // Improved error logging with structured information
    const logLoadingError = (message: string, error?: unknown) => {
      const errorDetails = error instanceof Error
        ? { message: error.message, stack: error.stack }
        : { details: String(error) };

      console.error(message, errorDetails);
    };

    // Enhanced model loading logging
    const logModelLoading = (path: string, success: boolean, error?: string) => {
      if (success) {
        console.log(`✅ Successfully loaded model: ${path}`);
      } else {
        console.error(`❌ Failed to load model: ${path}`, error);
      }
    };

    // Improved mesh loading with better error handling and fallback options
    const loadMesh = async (rootUrl: string, filename: string, partType: string): Promise<BABYLON.AbstractMesh[]> => {
      try {
        console.log(`Loading furniture part: ${rootUrl}${filename}.glb`);

        // Helper function to process loaded meshes
        const processMeshes = (meshes: BABYLON.AbstractMesh[], source: string) => {
          logModelLoading(source, true);

          meshes.forEach(mesh => {
            if (!mesh.parent) {
              mesh.position = new BABYLON.Vector3(0, 0, 0);
              mesh.scaling = new BABYLON.Vector3(1, 1, 1);
            }

            if (mesh instanceof BABYLON.Mesh) {
              applyMaterialToMesh(mesh, partType, filename);
            }
          });

          return meshes;
        };

        // Load GLB format
        try {
          // Create a container for the model
          const container = await BABYLON.SceneLoader.LoadAssetContainerAsync(
            rootUrl,
            filename + '.glb',
            scene
          );

          // Add all meshes to the scene
          container.addAllToScene();
          // Get all meshes from the container
          const meshes = container.meshes;
          return processMeshes(meshes, `${rootUrl}${filename}.glb`);
        } catch (glbError) {
          // GLB loading failed
          throw new Error(`Failed to load GLB file: ${String(glbError)}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logModelLoading(`${rootUrl}${filename}`, false, errorMessage);
        logLoadingError(`Error loading mesh from "${rootUrl}${filename}":`, error);

        // Return empty array when loading fails
        return [];
      }
    };

    // Helper function to map wood type to texture base name
    const getWoodTextureBaseName = (woodType: string): string => {
      let textureBaseName = woodType.replace('Ahsap', '').trim();

      // Map texture names that have spaces in file names
      if (woodType === 'CevizEskitmeAhsap') {
        textureBaseName = 'Ceviz Eskitme';
      } else if (woodType === 'GriEskitmeAhsap') {
        textureBaseName = 'Gri Eskitme';
      } else if (woodType === 'SariEskitmeAhsap') {
        textureBaseName = 'Sarı Eskitme';
      }

      return textureBaseName;
    };

    // Enhanced material application with better texture handling and fallbacks
    const applyMaterialToMesh = (mesh: BABYLON.Mesh, partType: string, partName: string) => {
      try {
        // Create a PBR material with fabric-like finish for better texture definition
        const material = new BABYLON.PBRMaterial(`${partName}-material`, scene);
        material.backFaceCulling = true; // Improve performance
        material.specularIntensity = 0; // Remove specular to avoid extra shininess
        material.roughness = 0.9; // Matte finish
        material.metallic = 0; // Non-metallic
        material.useRoughnessFromMetallicTextureAlpha = false;
        material.useRoughnessFromMetallicTextureGreen = false;
        material.useMetallnessFromMetallicTextureBlue = false;
        material.disableLighting = false;
        material.environmentIntensity = 0.1; // Lower to reduce overall shine
        material.microSurface = 0.6; // More diffuse
        material.alpha = 1.0; // Ensure full opacity
        // Add ambient color to make materials brighter overall
        material.ambientColor = new BABYLON.Color3(0.3, 0.3, 0.3);

        // Improved texture loading with multiple fallback paths
        const applyTexture = (texturePath: string) => {
          try {
            console.log(`Attempting to load texture: ${texturePath}`);
            // Use appropriate extension based on texture type
            let ext = ".jpg";
            // Check if this is a metal leg texture that uses .bmp
            if (texturePath.includes('/leg/') && (texturePath.includes('Bronz') || texturePath.includes('Gold') || texturePath.includes('nickel'))) {
              ext = ".bmp";
            }
            // Try different file extensions if the primary one fails
            const tryTextureWithExtensions = (basePath: string): BABYLON.Texture | null => {
              try {
                const fullPath = `${basePath}${ext}`;
                const texture = new BABYLON.Texture(
                  fullPath,
                  scene
                  // Use all Babylon defaults - no custom parameters
                );

                console.log(`Successfully loaded texture: ${fullPath}`);
                return texture;
              } catch (textureError) {
                console.warn(`Failed to load texture with extension ${ext}:`, textureError);
                // Continue to next extension
              }
              return null; // All extensions failed
            };

            // Extract base path without extension
            const basePath = texturePath.replace(/\.[^/.]+$/, "");

            // Try multiple extensions
            return tryTextureWithExtensions(basePath);
          } catch (error) {
            console.warn(`Failed to load texture ${texturePath}:`, error);
            return null;
          }
        };

        // Get fabric type and color from design
        let fabricType = design.fabric?.type || 'cartela1';
        let fabricColor = design.fabric?.color || '1';

        // Special handling for klapa and papel parts
        if (partType === 'armrest') {
          // For klapa (3) and papel (5) parts, use klapa fabric
          if (/^(3|5)(-|\.glb|$)/.test(partName)) {
            // These parts should use the klapa fabric
            fabricType = 'klapa';
            fabricColor = '1';
          }
        }

        // Based on the part type, assign different textures or colors with improved fallbacks
        console.log(`Applying material to part: ${partName}, type: ${partType}`);

        if (partType === 'woodVeneer' || partName.includes('AhsapKasa')) {
          // Wood parts - try texture first, fall back to color
          const woodType = design.woodVeneerFabric || 'CevizAhsap';
          console.log(`Applying wood texture type: ${woodType} to ${partName}`);

          // For wooden parts, use _diffuseOriginal.jpg naming convention
          const textureBaseName = getWoodTextureBaseName(woodType);

          const texturePaths = [
            `/assets/fabric/wooden/${woodType}/${textureBaseName}_diffuseOriginal`
          ];

          let texture = null;
          for (const path of texturePaths) {
            texture = applyTexture(path);
            if (texture) break;
          }

          if (texture) {
            material.albedoTexture = texture;
            console.log(`✅ Successfully applied wooden texture to ${mesh.name}`);
          } else {
            console.warn(`⚠️ Failed to load wooden texture for ${mesh.name}, falling back to color`);
            // Fallback to color based on wood type
            let woodColor;
            if (woodType.includes('Ceviz')) {
              woodColor = new BABYLON.Color3(0.6, 0.4, 0.2); // Walnut
            } else if (woodType.includes('Beyaz')) {
              woodColor = new BABYLON.Color3(0.9, 0.9, 0.85); // White
            } else if (woodType.includes('Gri')) {
              woodColor = new BABYLON.Color3(0.6, 0.6, 0.6); // Grey
            } else if (woodType.includes('Antrasit')) {
              woodColor = new BABYLON.Color3(0.3, 0.3, 0.3); // Anthracite
            } else {
              woodColor = new BABYLON.Color3(0.6, 0.4, 0.2); // Default walnut
            }

            material.albedoColor = woodColor;
            console.log(`Applied wooden color to ${mesh.name} based on type: ${woodType}`);
          }
        }
        else if (partType === 'legs' || partName.includes('leg') || partName.includes('ayak')) {
          // Leg parts - try texture first, fall back to color
          const legType = design.legFabric || 'CevizAhsap';

          // For leg parts, use _diffuseOriginal naming convention
          const texturePaths = [
            `/assets/fabric/leg/${legType}/${legType}_diffuseOriginal`
          ];

          let texture = null;
          for (const path of texturePaths) {
            texture = applyTexture(path);
            if (texture) break;
          }

          if (texture) {
            material.albedoTexture = texture;
            console.log(`Applied leg texture to ${mesh.name}`);
          } else {
            // Fallback to color based on leg type
            let legColor;
            if (legType.includes('Metal')) {
              legColor = new BABYLON.Color3(0.7, 0.7, 0.7); // Metal
            } else if (legType.includes('Ahşap')) {
              legColor = new BABYLON.Color3(0.6, 0.4, 0.2); // Wood
            } else {
              legColor = new BABYLON.Color3(0.5, 0.5, 0.5); // Default
            }

            material.albedoColor = legColor;
            console.log(`Applied leg color to ${mesh.name} based on type: ${legType}`);
          }
        }
        else if (partType === 'skeleton') {
          // For bergere skeleton, apply wood texture from skeleton color in design
          const woodType = design.skeleton?.color || design.woodVeneerFabric || 'CevizAhsap';
          console.log(`Applying wood texture to skeleton: ${woodType}`);

          // Map texture names that have spaces in file names
          const textureBaseName = getWoodTextureBaseName(woodType);

          const texturePaths = [
            `/assets/fabric/wooden/${woodType}/${textureBaseName}_diffuseOriginal`
          ];

          let texture = null;
          for (const path of texturePaths) {
            texture = applyTexture(path);
            if (texture) break;
          }

          if (texture) {
            material.albedoTexture = texture;
            console.log(`✅ Successfully applied wooden texture to skeleton ${mesh.name}`);
          } else {
            // Fallback to wood color
            material.albedoColor = new BABYLON.Color3(0.6, 0.4, 0.2); // Default walnut
            console.log(`Applied wood color to skeleton ${mesh.name}`);
          }
        }
        else if (partType === 'cushion' || partType === 'seat' || partType === 'armrest' || partType === 'lowerFrame') {
          console.log(`Applying fabric texture to ${partName}, fabric type: ${fabricType}, color: ${fabricColor}`);
          // Build texture paths based on fabric type
          let texturePaths = [];

          if (fabricType === 'klapa') {
            // For klapa fabric, use the direct path
            texturePaths = [`/assets/fabric/klapa/1`];
          } else if (fabricType.startsWith('cartela')) {
            // For all cartelas (cartela1 through cartela12)
            // Extract the cartela number from the fabric type
            const cartelaNumber = fabricType.replace('cartela', '');
            // Path structure: fabric/cartela/{cartela_number}/{color}.jpg
            texturePaths.push(`/assets/fabric/cartela/${cartelaNumber}/${fabricColor}`);
          } else if (fabricType === 'brown' || fabricType === 'greenCotton' || fabricType === 'grey') {
            // For brown, greenCotton, and grey - they have a similar structure but with subfolder "1"
            // Try numbered files within the subfolder
            for (let i = 1; i <= 10; i++) {
              texturePaths.push(`/assets/fabric/cartela/${fabricType}/1/${i}`);
            }
            // Also try the direct path in case there are direct jpg files
            texturePaths.push(`/assets/fabric/cartela/${fabricType}/1/1`);
          }

          let texture = null;
          for (const path of texturePaths) {
            // Use appropriate texture scale
            texture = applyTexture(path);
            if (texture) break;
          }

          if (texture) {
            material.albedoTexture = texture;
            console.log(`✅ Successfully applied fabric texture to ${mesh.name}`);
          } else {
            console.warn(`⚠️ Failed to load fabric texture for ${mesh.name}, falling back to color`);
            // Fallback to a color based on fabric type with more options
            let fabricColorValue;
            if (fabricType.includes('brown')) {
              fabricColorValue = new BABYLON.Color3(0.55, 0.35, 0.15);
            } else if (fabricType.includes('grey') || fabricType.includes('gray')) {
              fabricColorValue = new BABYLON.Color3(0.7, 0.7, 0.7);
            } else if (fabricType.includes('blue')) {
              fabricColorValue = new BABYLON.Color3(0.2, 0.3, 0.6);
            } else if (fabricType.includes('red')) {
              fabricColorValue = new BABYLON.Color3(0.6, 0.2, 0.2);
            } else if (fabricType.includes('green')) {
              fabricColorValue = new BABYLON.Color3(0.2, 0.5, 0.3);
            } else if (fabricType.includes('black')) {
              fabricColorValue = new BABYLON.Color3(0.2, 0.2, 0.2);
            } else if (fabricType.includes('white') || fabricType.includes('beyaz')) {
              fabricColorValue = new BABYLON.Color3(0.9, 0.9, 0.9);
            } else {
              fabricColorValue = new BABYLON.Color3(0.8, 0.8, 0.65); // Default beige
            }

            material.albedoColor = fabricColorValue;
            console.log(`Applied fabric color to ${mesh.name} based on type: ${fabricType}`);
          }
        }
        else {
          // Default color for any other parts
          material.albedoColor = new BABYLON.Color3(0.7, 0.7, 0.7);
          console.log(`Applied default color to ${mesh.name}`);
        }

        // Make meshes cast and receive shadows
        mesh.receiveShadows = true;

        // Add to shadow casters if shadow generator exists
        if (shadowGenerator.current) {
          shadowGenerator.current.addShadowCaster(mesh);
          console.log(`Added ${mesh.name} to shadow casters`);
        }

        // Apply the material to the mesh
        mesh.material = material;

      } catch (error) {
        console.error(`Error applying material to mesh ${mesh.name}:`, error);

        // Apply fallback material (also using PBR for consistency)
        try {
          const defaultMaterial = new BABYLON.PBRMaterial('fallback-material', scene);
          defaultMaterial.albedoColor = new BABYLON.Color3(0.7, 0.7, 0.7);
          defaultMaterial.roughness = 1.0;
          defaultMaterial.metallic = 0;
          defaultMaterial.specularIntensity = 0;
          defaultMaterial.environmentIntensity = 0;
          mesh.material = defaultMaterial;
        } catch (fallbackError) {
          console.error('Failed to apply fallback material:', fallbackError);
        }
      }
    };

    // Create a parent mesh for all furniture parts
    const furnitureParent = new BABYLON.Mesh('furnitureParent', scene);

    // Enhanced scene setup with improved camera and lighting
    const setupScene = () => {
      // Create an arc rotate camera with better initial position
      const camera = new BABYLON.ArcRotateCamera(
        'camera',
        -Math.PI / 2,  // Alpha (rotation around Y axis)
        Math.PI / 2.5, // Beta (rotation around X axis)
        4,             // Radius (distance from target)
        new BABYLON.Vector3(0, 0, 0), // Target position
        scene
      );

      // Attach camera controls to canvas with improved settings
      camera.attachControl(canvasRef.current, true);
      camera.lowerRadiusLimit = 2.5;     // Minimum zoom distance
      camera.upperRadiusLimit = 3.5;    // Maximum zoom distance
      camera.wheelDeltaPercentage = 0.01; // Smoother zooming
      camera.panningSensibility = 50;  // Panning sensitivity

      // Set beta (vertical angle) constraints to prevent viewing from below the furniture
      camera.lowerBetaLimit = 0.6;     // Prevent camera from going below furniture (about 34 degrees)
      camera.upperBetaLimit = Math.PI / 2; // Limit to horizontal view (90 degrees)

      camera.useBouncingBehavior = false; // Bounce back when hitting limits
      camera.useAutoRotationBehavior = false; // Disable auto-rotation

      // Add inertia for smoother camera movement
      camera.inertia = 0.7;
      // Add ambient light to brighten the overall scene
      const hemisphericLight = new BABYLON.HemisphericLight(
        'ambientLight',
        new BABYLON.Vector3(0, 1, 0),
        scene
      );
      hemisphericLight.intensity = 0.5; // Medium ambient intensity
      hemisphericLight.diffuse = new BABYLON.Color3(1, 1, 1);
      hemisphericLight.groundColor = new BABYLON.Color3(0.5, 0.5, 0.5); // Reflect some light from ground

      // Create main directional light (strong sun from left side)
      const directionalLight = new BABYLON.DirectionalLight(
        'directionalLight',
        // Direction vector points toward where the light is going
        new BABYLON.Vector3(1, -0.2, 0.1), // More horizontal angle from left
        scene
      );
      // Position doesn't affect direction for directional lights but helps with shadows
      directionalLight.position = new BABYLON.Vector3(-10, 2, 0); // Further left, lower height
      directionalLight.intensity = 2.0; // Even stronger light
      directionalLight.diffuse = new BABYLON.Color3(1, 0.95, 0.8); // Warm sunlight
      directionalLight.specular = new BABYLON.Color3(0.1, 0.1, 0.1); // Small specular

      // Create shadow generator with higher resolution and contrast
      shadowGenerator.current = new BABYLON.ShadowGenerator(4096, directionalLight);
      shadowGenerator.current.useBlurExponentialShadowMap = true;
      shadowGenerator.current.blurKernel = 32; // Sharper shadow edges for more definition
      shadowGenerator.current.darkness = 0.7; // Darker shadows for more visibility
      shadowGenerator.current.depthScale = 30; // Tighter depth scale

      // Add ground plane to receive shadows
      const ground = BABYLON.MeshBuilder.CreateGround("shadowGround", { width: 20, height: 20 }, scene);
      ground.position.y = -0.05; // Just below furniture
      ground.receiveShadows = true;
      ground.isVisible = false; // Invisible but still receives shadows

      // Reduced fill light to ensure shadows remain prominent
      const fillLight = new BABYLON.DirectionalLight(
        'fillLight',
        new BABYLON.Vector3(-0.5, -0.2, 0), // From right to left, slightly down
        scene
      );
      fillLight.position = new BABYLON.Vector3(3, 2, 0);
      fillLight.intensity = 0.2; // Weaker fill to maintain shadow contrast
      fillLight.diffuse = new BABYLON.Color3(0.8, 0.8, 1.0); // Slightly cool fill

      // Enable physics if needed
      // scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), new BABYLON.CannonJSPlugin());
    };

    // Enhanced furniture parts loading with better error handling and progress tracking
    const loadFurnitureParts = async () => {
      try {
        console.log('Starting to load furniture parts with design:', design);

        // Clear existing furniture parts before loading new ones to prevent duplicates
        console.log('Clearing existing furniture parts...');
        const existingChildren = furnitureParent.getChildMeshes();
        existingChildren.forEach(mesh => {
          try {
            // Dispose material first if it exists
            if (mesh.material) {
              mesh.material.dispose();
            }
            // Dispose the mesh
            mesh.dispose();
          } catch (disposeError) {
            console.warn(`Error disposing mesh ${mesh.name}:`, disposeError);
          }
        });
        console.log(`Cleared ${existingChildren.length} existing furniture parts`);

        // Define all parts to load with proper error handling for missing values
        let parts = [];

        if (furnitureType === 'bergere') {
          // For bergere, load skeleton and its corresponding seat
          console.log('🎯 Loading bergere with design:', design);

          // Extract just the number from seat type (e.g., "1" from "seatFile:1:BOŞ")
          let seatNumber = design.seat?.type || '1';
          if (seatNumber.includes(':')) {
            const parts = seatNumber.split(':');
            seatNumber = parts[1] || '1';
          }

          // Use skeleton type for skeleton file, seat number for seat file
          const skeletonType = design.skeleton?.type || '1';

          console.log(`🎯 Bergere loading - skeleton type: ${skeletonType}, seat number: ${seatNumber}`);

          parts = [
            {
              path: getPartFilePath(furnitureType, 'skeleton', skeletonType),
              type: 'skeleton',
              required: true
            },
            {
              path: `/assets/${furnitureType}/skeleton/${skeletonType}/seat`,
              type: 'seat',
              required: true
            }
          ];

          // Try to load fabric if available
          if (design.fabric?.type && design.fabric?.color) {
            parts.push({
              path: getPartFilePath(furnitureType, 'fabric', `${design.fabric.type}/${design.fabric.color}`),
              type: 'fabric',
              required: false
            });
          }

          console.log('🎯 Bergere parts to load:', parts);
        } else {
          // For armchair, load all parts
          parts = [
            {
              path: getPartFilePath(furnitureType, 'lowerFrame', design.lowerFrame || '1'),
              type: 'lowerFrame',
              required: true // Mark essential parts
            },
            {
              path: getPartFilePath(furnitureType, 'legs', design.legs || '1'),
              type: 'legs',
              required: true
            },
            {
              path: getPartFilePath(furnitureType, 'woodVeneer', design.woodVeneer || '1'),
              type: 'woodVeneer',
              required: false // Optional parts
            },
            // Only load armrest main if it's explicitly set (not empty)
            ...(design.armrest?.main ? [{
              path: getPartFilePath(furnitureType, 'armrest', design.armrest.main),
              type: 'armrest',
              required: true
            }] : []),
            {
              path: getPartFilePath(furnitureType, 'cushion', design.cushion?.option || '1'),
              type: 'cushion',
              required: true
            },
            {
              path: getPartFilePath(furnitureType, 'seat', design.seat?.type || '1'),
              type: 'seat',
              required: true
            },
          ];
        }

        // Add armrest sub-options if available (only for armchair)
        if (furnitureType !== 'bergere' && design.armrest?.sub && Array.isArray(design.armrest.sub) && design.armrest.sub.length > 0) {
          design.armrest.sub.forEach(subOption => {
            if (subOption) {
              parts.push({
                path: getPartFilePath(furnitureType, 'armrestSubOptions', subOption),
                type: 'armrest',
                required: false
              });
            }
          });
        }

        // Track loading progress
        let loadedCount = 0;
        const totalParts = parts.length;
        const requiredParts = parts.filter(p => p.required).length;
        let requiredPartsLoaded = 0;

        // Load each part with proper error handling
        for (const part of parts) {
          try {
            // Skip empty paths
            if (!part.path) {
              console.warn(`Skipping part with empty path: ${part.type}`);
              continue;
            }

            const lastSlashIndex = part.path.lastIndexOf('/');
            if (lastSlashIndex === -1) {
              console.warn(`Invalid path format for ${part.type}: ${part.path}`);
              continue;
            }

            const rootUrl = part.path.substring(0, lastSlashIndex + 1);
            let filename = part.path.substring(lastSlashIndex + 1);

            // Remove .glb extension if present since loadMesh will add it
            if (filename.endsWith('.glb')) {
              filename = filename.slice(0, -4);
            }

            console.log(`Loading part ${loadedCount + 1}/${totalParts}: ${part.type} - ${filename}`);
            const meshes = await loadMesh(rootUrl, filename, part.type);

            // Attach meshes to parent
            meshes.forEach(mesh => {
              if (!mesh.parent) {
                mesh.parent = furnitureParent;
              }
            });

            // Update progress
            loadedCount++;
            if (part.required) requiredPartsLoaded++;

            console.log(`Progress: ${loadedCount}/${totalParts} parts loaded (${requiredPartsLoaded}/${requiredParts} required parts)`);
          } catch (error) {
            console.error(`Failed to load part ${part.path}:`, error);

            // For required parts, try to load a fallback model
            if (part.required) {
              try {
                console.warn(`Attempting to load fallback for required part: ${part.type}`);
                const fallbackPath = getPartFilePath(furnitureType, part.type, '1'); // Default to model 1

                if (fallbackPath && fallbackPath !== part.path) {
                  const lastSlashIndex = fallbackPath.lastIndexOf('/');
                  const rootUrl = fallbackPath.substring(0, lastSlashIndex + 1);
                  let filename = fallbackPath.substring(lastSlashIndex + 1);

                  // Remove .glb extension if present since loadMesh will add it
                  if (filename.endsWith('.glb')) {
                    filename = filename.slice(0, -4);
                  }

                  const fallbackMeshes = await loadMesh(rootUrl, filename, part.type);
                  fallbackMeshes.forEach(mesh => {
                    if (!mesh.parent) {
                      mesh.parent = furnitureParent;
                    }
                  });

                  requiredPartsLoaded++;
                  console.log(`Loaded fallback for ${part.type}`);
                }
              } catch (fallbackError) {
                console.error(`Failed to load fallback for required part ${part.type}:`, fallbackError);
              }
            }
          }
        }

        // Center the furniture after loading all parts
        if (loadedCount > 0) {
          // Compute bounding box of all loaded meshes
          const boundingInfo = furnitureParent.getHierarchyBoundingVectors(true);
          const center = new BABYLON.Vector3(
            (boundingInfo.max.x + boundingInfo.min.x) / 2,
            (boundingInfo.max.y + boundingInfo.min.y) / 2,
            (boundingInfo.max.z + boundingInfo.min.z) / 2
          );

          // Center the furniture
          furnitureParent.position = new BABYLON.Vector3(-center.x, -center.y, -center.z);

          console.log(`Furniture centered at position: ${furnitureParent.position.toString()}`);
        }

        // Log loading summary
        console.log(`Furniture loading complete: ${loadedCount}/${totalParts} parts loaded successfully`);
        console.log(`Required parts: ${requiredPartsLoaded}/${requiredParts} loaded successfully`);

        if (requiredPartsLoaded < requiredParts) {
          console.warn('Some required furniture parts could not be loaded!');
        }
      } catch (error) {
        console.error('Error loading furniture parts:', error);
      }
    };

    // Initialize the scene and load furniture
    setupScene();
    loadFurnitureParts();

    // Performance monitoring is disabled because @babylonjs/inspector is not installed
    // To enable it, install the package with: npm install --save @babylonjs/inspector
    // Then uncomment the code below:
    /*
    if (process.env.NODE_ENV === 'development') {
      // This requires @babylonjs/inspector to be installed
      scene.debugLayer.show({
        embedMode: true,
        showInspector: false,
        overlay: true
      });
    }
    */

    // Start the render loop with error handling
    let renderLoopStarted = false;
    try {
      engine.runRenderLoop(() => {
        try {
          scene.render();
        } catch (renderError) {
          console.error('Error during scene rendering:', renderError);
          // Only stop the render loop if it's a critical error
          if (renderError instanceof Error &&
            (renderError.message.includes('WebGL') || renderError.message.includes('GPU'))) {
            engine.stopRenderLoop();
            console.error('Critical rendering error, stopped render loop');
          }
        }
      });
      renderLoopStarted = true;
    } catch (startError) {
      console.error('Failed to start render loop:', startError);
    }

    // Handle window resize events
    const resizeObserver = new ResizeObserver(() => {
      try {
        engine.resize();
      } catch (resizeError) {
        console.warn('Error during resize:', resizeError);
      }
    });

    if (canvasRef.current) {
      resizeObserver.observe(canvasRef.current);
    }

    // Cleanup function with improved error handling
    return () => {
      console.log('Cleaning up BabylonJS resources');

      // Disconnect resize observer
      try {
        resizeObserver.disconnect();
      } catch (observerError) {
        console.warn('Error disconnecting resize observer:', observerError);
      }

      // Stop render loop if it was started
      if (renderLoopStarted) {
        try {
          engine.stopRenderLoop();
        } catch (stopError) {
          console.warn('Error stopping render loop:', stopError);
        }
      }

      // Dispose scene and engine
      try {
        // Dispose all meshes first
        if (scene) {
          scene.meshes.forEach(mesh => {
            try {
              if (mesh.material) {
                mesh.material.dispose();
              }
              mesh.dispose();
            } catch (meshError) {
              console.warn(`Error disposing mesh ${mesh.name}:`, meshError);
            }
          });

          // Dispose lights
          scene.lights.forEach(light => {
            try {
              light.dispose();
            } catch (lightError) {
              console.warn(`Error disposing light ${light.name}:`, lightError);
            }
          });

          // Dispose cameras
          scene.cameras.forEach(camera => {
            try {
              camera.dispose();
            } catch (cameraError) {
              console.warn(`Error disposing camera ${camera.name}:`, cameraError);
            }
          });

          // Dispose textures
          scene.textures.forEach(texture => {
            try {
              texture.dispose();
            } catch (textureError) {
              console.warn(`Error disposing texture:`, textureError);
            }
          });

          // Finally dispose scene and engine
          scene.dispose();
        }

        if (engine) {
          engine.dispose();
        }
      } catch (disposeError) {
        console.warn('Error during cleanup:', disposeError);
      }
    };
  }, [design, furnitureType, getPartFilePath, backCushion, decorativeCushions]);

  // Return canvas element with ref and improved styling
  return (
    <canvas
      ref={canvasRef}
      onWheel={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      style={{
        width: '100%',
        height: '100%',
        outline: 'none',
        touchAction: 'none' // Prevent touch actions for better mobile experience
      }}
    />
  );
}