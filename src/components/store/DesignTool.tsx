import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { FiSave, FiShoppingCart, FiRotateCw } from 'react-icons/fi'
import { But<PERSON> } from '@/components/common/Button'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card'

const designOptions = {
  sofaType: ['3-seater sofa', 'armchair', 'L-shaped sectional', 'Chaise lounge'],
  fabric: ['Velvet', 'Leather', 'Linen', 'Cotton', 'Microfiber', 'Chenille'],
  color: ['Red', 'Blue', 'Green', 'Yellow', 'Black', 'White', 'Gray', 'Purple', 'Orange', 'Teal'],
  legs: ['Wooden', 'Metal', 'Plastic', 'Acrylic', 'Hairpin', 'Tapered'],
}

export function DesignTool() {
  const [design, setDesign] = useState({
    sofaType: '',
    fabric: '',
    color: '',
    legs: '',
  })

  const handleDesignChange = (option: string, value: string) => {
    setDesign((prev) => ({ ...prev, [option]: value }))
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-6xl mx-auto p-8"
    >
      <Card className="bg-gradient-to-br from-primary to-secondary shadow-2xl">
        <CardHeader className="bg-white bg-opacity-10 backdrop-blur-lg">
          <CardTitle className="text-4xl font-bold text-white text-center">Sofa Design Studio</CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div className="space-y-6">
              {Object.entries(designOptions).map(([option, values]) => (
                <motion.div
                  key={option}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * Object.keys(designOptions).indexOf(option) }}
                >
                  <label className="block text-lg font-semibold text-white mb-2">
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </label>
                  <select
                    value={design[option as keyof typeof design]}
                    onChange={(e) => handleDesignChange(option, e.target.value)}
                    className="w-full px-4 py-3 text-gray-700 bg-white bg-opacity-80 rounded-lg focus:outline-none focus:ring-2 focus:ring-accent transition duration-200 ease-in-out transform hover:scale-105"
                  >
                    <option value="">Select {option}</option>
                    {values.map((value) => (
                      <option key={value} value={value}>
                        {value}
                      </option>
                    ))}
                  </select>
                </motion.div>
              ))}
            </div>
            <motion.div
              className="bg-white bg-opacity-20 rounded-2xl flex items-center justify-center p-8 shadow-inner"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="text-center">
                <FiRotateCw className="w-24 h-24 text-white opacity-50 mx-auto mb-4 animate-spin-slow" />
                <p className="text-white text-xl font-semibold">Your custom sofa preview will appear here</p>
                <p className="text-white text-opacity-70 mt-2">Select options to see your design come to life!</p>
              </div>
            </motion.div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between bg-white bg-opacity-10 backdrop-blur-lg p-6">
          <Button variant="outline" className="flex items-center bg-white bg-opacity-20 text-white hover:bg-white hover:text-primary transition duration-300 ease-in-out transform hover:scale-105">
            <FiSave className="mr-2" />
            Save Design
          </Button>
          <Button className="flex items-center bg-accent text-white hover:bg-accent-light transition duration-300 ease-in-out transform hover:scale-105">
            <FiShoppingCart className="mr-2" />
            Add to Cart
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  )
}