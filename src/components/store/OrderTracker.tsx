// Order tracking component that displays current status of an order
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>heck } from 'react-icons/fi'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/common/Card'

type Order = {
  id: string
  userId: string
  orderDate: string
  status: string
  storeName?: string
  totalPrice: number
  items: OrderItem[]
  estimatedDelivery?: string
}

type OrderItem = {
  quantity: number
  designJson: string
}

type OrderTrackerProps = {
  order: Order
}

export function OrderTracker({ order }: OrderTrackerProps) {
  const steps = [
    { icon: FiPackage, label: 'Order Placed', completed: true },
    { icon: FiTruck, label: 'In Production', completed: order.status.toLowerCase() === 'approved' || order.status.toLowerCase() === 'in production' || order.status.toLowerCase() === 'completed' },
    { icon: FiCheck, label: 'Completed', completed: order.status.toLowerCase() === 'completed' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center">
              <div className={`rounded-full p-2 ${step.completed ? 'bg-primary text-primary-foreground' : 'bg-gray-200 text-gray-400'}`}>
                <div className="w-6 h-6">
                  <step.icon />
                </div>
              </div>
              <span className="mt-2 text-sm font-medium">{step.label}</span>
            </div>
          ))}
        </div>
        <div className="mt-4">
          <p className="text-sm text-gray-600">Order ID: {order.id}</p>
          <p className="text-sm text-gray-600">Order Date: {new Date(order.orderDate).toLocaleDateString()}</p>
          {order.estimatedDelivery && <p className="text-sm text-gray-600">Estimated Delivery: {order.estimatedDelivery}</p>}
        </div>
      </CardContent>
    </Card>
  )
}