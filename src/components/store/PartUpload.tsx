import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/common/Button';
import { Input } from '@/components/common/Input';
import { api } from '@/services/api';

interface PartUploadProps {
  onSuccess?: () => void;
  className?: string;
}

export const PartUpload: React.FC<PartUploadProps> = ({ onSuccess, className = '' }) => {
  const { register, handleSubmit, reset, formState: { errors } } = useForm();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onSubmit = async (data: any) => {
    setIsUploading(true);
    setUploadProgress(0);
    
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('type', data.type);
    formData.append('subType', data.subType);
    formData.append('file', data.file[0]);
    
    if (data.textureFile && data.textureFile[0]) {
      formData.append('textureFile', data.textureFile[0]);
    }

    try {
      await api.post('/furniture/upload-part', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
          }
        }
      });
      
      reset();
      setUploadProgress(100);
      
      setTimeout(() => {
        setUploadProgress(0);
        if (onSuccess) onSuccess();
      }, 1000);
      
    } catch (error) {
      console.error('Error uploading part:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`bg-white p-6 rounded-lg shadow-md ${className}`}>
      <h2 className="text-xl font-semibold mb-4">Upload Furniture Part</h2>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <Input 
          label="Part Name" 
          {...register('name', { required: 'Part name is required' })} 
          error={errors.name?.message as string}
          className="mb-4" 
        />
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Furniture Type</label>
          <select 
            {...register('type', { required: 'Furniture type is required' })} 
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">Select type</option>
            <option value="armchair">Armchair</option>
            <option value="bergere">Bergere</option>
            <option value="sofa">Sofa</option>
            <option value="ottoman">Ottoman</option>
          </select>
          {errors.type && (
            <p className="mt-1 text-sm text-red-600">{errors.type.message as string}</p>
          )}
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Sub Type</label>
          <select 
            {...register('subType', { required: 'Sub type is required' })} 
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">Select sub type</option>
            <option value="armrest">Armrest</option>
            <option value="cushion">Cushion</option>
            <option value="woodVeneer">Wood Veneer</option>
            <option value="legs">Legs</option>
            <option value="lowerFrame">Lower Frame</option>
          </select>
          {errors.subType && (
            <p className="mt-1 text-sm text-red-600">{errors.subType.message as string}</p>
          )}
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">OBJ File</label>
          <input 
            type="file" 
            {...register('file', { required: 'OBJ file is required' })} 
            accept=".obj" 
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
          />
          {errors.file && (
            <p className="mt-1 text-sm text-red-600">{errors.file.message as string}</p>
          )}
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Texture File (optional)</label>
          <input 
            type="file" 
            {...register('textureFile')} 
            accept=".jpg,.jpeg,.png" 
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
          />
        </div>
        
        {uploadProgress > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div 
              className="bg-indigo-600 h-2.5 rounded-full" 
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}
        
        <Button type="submit" disabled={isUploading} className="w-full">
          {isUploading ? 'Uploading...' : 'Upload Part'}
        </Button>
      </form>
    </div>
  );
};