import React, { createContext, useContext, useState } from 'react'

interface TabsContextType {
  activeTab: string
  setActiveTab: (value: 'armchair' | 'bergere') => void
}

const TabsContext = createContext<TabsContextType | undefined>(undefined)

interface TabsProps {
  children: React.ReactNode
  value: string
  onValueChange: (value: 'armchair' | 'bergere') => void
}

interface TabsListProps {
  children: React.ReactNode
  className?: string
}

interface TabsTriggerProps {
  value: 'armchair' | 'bergere'
  children: React.ReactNode
}

interface TabsContentProps {
  value: string
  children: React.ReactNode
}

export const Tabs: React.FC<TabsProps> = ({
  children,
  value,
  onValueChange,
}) => {
  const [activeTab, setActiveTab] = useState(value)

  const handleTabChange = (newValue: 'armchair' | 'bergere') => {
    setActiveTab(newValue)
    onValueChange(newValue)
  }

  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab: handleTabChange }}>
      <div className="w-full">{children}</div>
    </TabsContext.Provider>
  )
}

export const TabsList: React.FC<TabsListProps> = ({ children, className }) => {
  return (
    <div className={`flex border-b border-gray-200 ${className || ''}`}>
      {children}
    </div>
  )
}

export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  value,
  children,
}) => {
  const context = useContext(TabsContext)
  if (!context) {
    throw new Error('TabsTrigger must be used within a Tabs component')
  }
  const { activeTab, setActiveTab } = context

  return (
    <button
      className={`px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 focus:outline-none focus:text-gray-800 ${
        activeTab === value ? 'border-b-2 border-gray-800' : ''
      }`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  )
}

export const TabsContent: React.FC<TabsContentProps> = ({
  value,
  children,
}) => {
  const context = useContext(TabsContext)
  if (!context) {
    throw new Error('TabsContent must be used within a Tabs component')
  }
  const { activeTab } = context

  if (activeTab !== value) {
    return null
  }

  return <div className="mt-4">{children}</div>
}
