import React from 'react'
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/common/Button'
import { Input } from '@/components/common/Input'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/common/Card'
import { useTranslation } from 'react-i18next'

const priceSchema = z.object({
  sofaType: z.object({
    threeSeater: z.number().min(0, 'Price must be positive'),
    armchair: z.number().min(0, 'Price must be positive'),
  }),
  fabric: z.record(z.number().min(0, 'Price must be positive')),
  backPillow: z.object({
    none: z.number().min(0, 'Price must be positive'),
    filled: z.number().min(0, 'Price must be positive'),
    foam: z.number().min(0, 'Price must be positive'),
  }),
  cushions: z.object({
    none: z.number().min(0, 'Price must be positive'),
    small: z.number().min(0, 'Price must be positive'),
    medium: z.number().min(0, 'Price must be positive'),
    large: z.number().min(0, 'Price must be positive'),
  }),
  armrest: z.record(z.number().min(0, 'Price must be positive')),
  seat: z.record(z.number().min(0, 'Price must be positive')),
  lowerFrame: z.record(z.number().min(0, 'Price must be positive')),
  legs: z.record(z.number().min(0, 'Price must be positive')),
})

type PriceFormData = z.infer<typeof priceSchema>

type PriceFormProps = {
  onSubmit: (data: PriceFormData) => void
  initialData?: PriceFormData
}

export function PriceForm({ onSubmit, initialData }: PriceFormProps) {
  const { t } = useTranslation('admin');
  const { register, handleSubmit, formState: { errors } } = useForm<PriceFormData>({
    resolver: zodResolver(priceSchema),
    defaultValues: initialData,
  })

  const handleFormSubmit: SubmitHandler<PriceFormData> = (data) => {
    onSubmit(data)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('setComponentPrices')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <CardContent className="space-y-6">
          {Object.entries(initialData || {}).map(([category, items]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-2 text-primary">{category.charAt(0).toUpperCase() + category.slice(1)}</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {Object.entries(items).map(([item]) => (
                  <div key={item}>
                    <Input
                      label={item}
                      type="number"
                      {...register(`${category}.${item}` as any, { valueAsNumber: true })}
                      error={(errors[category as keyof PriceFormData] as any)?.[item]?.message}
                    />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
        <CardFooter>
          <Button type="submit">{t('savePrices')}</Button>
        </CardFooter>
      </form>
    </Card>
  )
}