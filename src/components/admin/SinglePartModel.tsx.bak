import React, { useEffect, useRef } from 'react';
import * as BABYLON from '@babylonjs/core';
import '@babylonjs/loaders';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card';
import { useTranslation } from 'react-i18next';

interface SinglePartModelProps {
  partId: string;
  name: string;
  type: string;
  subType: string;
  modelUrl: string;
  textureUrl?: string;
}

// Using a regular React component instead of FC type
export const SinglePartModel = ({
  partId,
  name,
  type,
  subType,
  modelUrl,
  textureUrl
}: SinglePartModelProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // Using mutable refs for Babylon objects
  const engineRef = useRef<BABYLON.Engine | null>(null);
  const sceneRef = useRef<BABYLON.Scene | null>(null);

  useEffect(() => {
    // Safety check for canvas element
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Initialize Babylon.js engine and scene
    const engine = new BABYLON.Engine(canvas, true);
    // Using a mutable object to store engine ref
    const engineRefValue = engineRef as { current: BABYLON.Engine | null };
    engineRefValue.current = engine;
    
    const scene = new BABYLON.Scene(engine);
    // Using a mutable object to store scene ref
    const sceneRefValue = sceneRef as { current: BABYLON.Scene | null };
    sceneRefValue.current = scene;
    
    // Set up camera
    const camera = new BABYLON.ArcRotateCamera(
      'camera',
      -Math.PI / 2,
      Math.PI / 3,
      10,
      new BABYLON.Vector3(0, 0, 0),
      scene
    );
    camera.attachControl(canvas, true);
    camera.wheelPrecision = 50;
    camera.lowerRadiusLimit = 3;
    camera.upperRadiusLimit = 20;
    
    // Add lighting
    const light1 = new BABYLON.HemisphericLight(
      'light1',
      new BABYLON.Vector3(0, 1, 0),
      scene
    );
    light1.intensity = 0.7;
    
    const light2 = new BABYLON.DirectionalLight(
      'light2',
      new BABYLON.Vector3(0, -1, 1),
      scene
    );
    light2.intensity = 0.5;
    
    // Set up environment
    scene.clearColor = new BABYLON.Color4(0.9, 0.9, 0.9, 1);
    
    // Load the 3D model
    BABYLON.SceneLoader.ImportMesh(
      '',
      '',
      modelUrl,
      scene,
      (meshes) => {
        // Center and scale the model
        const rootMesh = meshes[0];
        
        // Compute bounding box
        const boundingInfo = calculateBoundingInfo(meshes);
        if (boundingInfo) {
          // Center model
          const center = boundingInfo.boundingBox.centerWorld;
          rootMesh.position = new BABYLON.Vector3(-center.x, -center.y, -center.z);
          
          // Scale model to reasonable size
          const size = boundingInfo.boundingBox.extendSizeWorld;
          const maxDimension = Math.max(size.x, size.y, size.z);
          const scale = 5 / maxDimension;
          rootMesh.scaling = new BABYLON.Vector3(scale, scale, scale);
        }
        
        // Apply texture if provided
        if (textureUrl) {
          const material = new BABYLON.StandardMaterial('texture', scene);
          material.diffuseTexture = new BABYLON.Texture(textureUrl, scene);
          
          // Apply material to all meshes
          meshes.forEach(mesh => {
            if (mesh instanceof BABYLON.Mesh) {
              mesh.material = material;
            }
          });
        }
        
        // Enable model rotation
        scene.registerBeforeRender(() => {
          rootMesh.rotation.y += 0.005;
        });
      },
      (event) => {
        // Loading progress
        console.log(`Loading progress: ${event.loaded} / ${event.total}`);
      },
      (scene, message, exception) => {
        // Error handler
        console.error('Error loading model:', message, exception);
      }
    );
    
    // Start render loop
    engine.runRenderLoop(() => {
      scene.render();
    });
    
    // Handle window resize
    const handleResize = () => {
      engine.resize();
    };
    window.addEventListener('resize', handleResize);
    
    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize);
      scene.dispose();
      engine.dispose();
    };
  }, [modelUrl, textureUrl]);
  
  // Helper function to calculate bounding info for all meshes
  const calculateBoundingInfo = (meshes: BABYLON.AbstractMesh[]) => {
    if (meshes.length === 0) return null;
    
    // Get bounding info of all child meshes
    let minBoundingInfo = null;
    for (const mesh of meshes) {
      if (mesh instanceof BABYLON.Mesh && mesh.getBoundingInfo()) {
        const boundingInfo = mesh.getBoundingInfo();
        if (!minBoundingInfo) {
          minBoundingInfo = new BABYLON.BoundingInfo(
            boundingInfo.minimum.clone(),
            boundingInfo.maximum.clone()
          );
        } else {
          // Create a new bounding info that encompasses both
          const min = BABYLON.Vector3.Minimize(minBoundingInfo.minimum, boundingInfo.minimum);
          const max = BABYLON.Vector3.Maximize(minBoundingInfo.maximum, boundingInfo.maximum);
          minBoundingInfo = new BABYLON.BoundingInfo(min, max);
        }
      }
    }
    return minBoundingInfo;
  };

  return (
    <Card className="w-full shadow-md">
      <CardHeader>
        <CardTitle>{name}</CardTitle>
        <div className="text-sm text-gray-500">
          {type} - {subType}
        </div>
      </CardHeader>
      <CardContent>
        <canvas 
          ref={canvasRef} 
          style={{ width: '100%', height: '300px', touchAction: 'none' }}
        />
        <div className="mt-4 text-sm text-gray-700">
          <p><span className="font-medium">{t('partId')}:</span> {partId}</p>
          <p><span className="font-medium">{t('type')}:</span> {type}</p>
          <p><span className="font-medium">{t('subType')}:</span> {subType}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default SinglePartModel;