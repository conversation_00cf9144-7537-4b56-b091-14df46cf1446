import { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { AuthForm } from './AuthForm'
import { toast } from 'react-toastify'
import { api } from '@/services/api'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/hooks/useAuth'

interface LoginFormData {
  username: string
  password: string
}

export function Login() {
  const [loading, setLoading] = useState<boolean>(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { t } = useTranslation(['auth', 'common'])
  const { login } = useAuth()

  // Get redirect path from location state or default to home
  const from = (location.state as { from?: string })?.from || '/'

  const handleLogin = async (data: LoginFormData) => {
    try {
      setLoading(true)
      const response = await api.post('/auth/login', data)
      
      // Store authentication token
      login(response.data.token, response.data.user)
      
      toast.success(t('loginSuccess', { ns: 'auth' }))
      
      // Redirect to the original requested page or home
      navigate(from, { replace: true })
    } catch (error) {
      console.error('Login error:', error)
      toast.error(t('loginFailed', { ns: 'auth' }))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex items-center justify-center min-h-[70vh] px-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary">
            {t('welcomeBack', { ns: 'auth' })}
          </h1>
          <p className="text-gray-600 mt-2">
            {t('loginToContinue', { ns: 'auth' })}
          </p>
        </div>
        
        <AuthForm onLogin={handleLogin} />
        
        <div className="mt-6 text-center">
          <p className="text-gray-600">
            {t('dontHaveAccount', { ns: 'auth' })}{' '}
            <button
              onClick={() => navigate('/register')}
              className="text-primary hover:underline font-medium"
            >
              {t('signUp', { ns: 'auth' })}
            </button>
          </p>
          <button
            onClick={() => navigate('/forgot-password')}
            className="text-sm text-gray-500 hover:text-primary mt-2"
          >
            {t('forgotPassword', { ns: 'auth' })}
          </button>
        </div>
      </div>
    </div>
  )
}

export default Login
