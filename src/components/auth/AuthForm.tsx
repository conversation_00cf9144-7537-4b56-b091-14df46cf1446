import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { FiMail, FiLock } from 'react-icons/fi'
import { Button } from '@/components/common/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/Card'
import { Input } from '@/components/common/Input'
import { useTranslation } from 'react-i18next'

const loginSchema = z.object({
  username: z.string().email('Invalid email format').min(3, 'Email must be at least 3 characters'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

type LoginFormData = z.infer<typeof loginSchema>

export interface AuthFormProps {
  onLogin: (data: LoginFormData) => void
}

export function AuthForm({ onLogin }: AuthFormProps) {
  const loginForm = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { t } = useTranslation('auth')

  const handleSubmit = async (data: LoginFormData) => {
    try {
      setIsSubmitting(true)
      await onLogin(data)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="w-full">
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">
            {t('signIn')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={loginForm.handleSubmit(handleSubmit)} className="space-y-4">
            <Input
              label={t('emailLabel')}
              type="email"
              icon={<FiMail className="text-muted-foreground" />}
              {...loginForm.register('username')}
              error={t(loginForm.formState.errors.username?.message || '')}
            />
            <Input
              label={t('passwordLabel')}
              type="password"
              icon={<FiLock className="text-muted-foreground" />}
              {...loginForm.register('password')}
              error={t(loginForm.formState.errors.password?.message || '')}
            />
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? t('signingIn') : t('signIn')}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
