import { Link, useLocation } from 'react-router-dom'
import { FiSettings, FiPenTool, FiPackage } from 'react-icons/fi'
import { cn } from '@/utils/helpers'
import { useTranslation } from 'react-i18next'

const menuItems = [
  { icon: FiSettings, labelKey: 'settings', href: '/store/settings' },
  { icon: FiPenTool, labelKey: 'design', href: '/store/design' },
  { icon: FiPackage, labelKey: 'orders', href: '/store/orders' },
]

export function StoreSidebar() {
  const location = useLocation()
  const { t } = useTranslation('storeSidebar')

  return (
    <aside className="bg-accent text-primary w-64 min-h-screen p-6 shadow-lg">
      <nav className="space-y-2">
        {menuItems.map((item) => (
          <Link
            key={item.href}
            to={item.href}
            className={cn(
              'flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ease-in-out',
              location.pathname === item.href
                ? 'bg-primary text-white shadow-md'
                : 'text-secondary hover:bg-primary hover:text-white'
            )}
          >
            <item.icon className="w-5 h-5" />
            <span className="font-medium">{t(item.labelKey)}</span>
          </Link>
        ))}
      </nav>
    </aside>
  )
}