import { Link, useLocation } from 'react-router-dom'
import { FiHome, FiDollarSign, FiUsers, FiChevronRight, FiLayers } from 'react-icons/fi'
import { cn } from '@/utils/helpers'
import { useTranslation } from 'react-i18next'

const menuItems = [
  { icon: FiHome, labelKey: 'dashboard', href: '/admin' },
  { icon: FiDollarSign, labelKey: 'costManagement', href: '/admin/cost-management' },
  { icon: FiUsers, labelKey: 'userManagement', href: '/admin/user-management' },
]

export function AdminSidebar() {
  const location = useLocation()
  const { t } = useTranslation('adminSidebar')

  return (
    <aside className="bg-gradient-to-b from-purple-50 to-white text-gray-900 w-64 min-h-screen shadow-lg border-r border-gray-200 z-40">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <Link to="/admin" className="flex items-center space-x-3 group">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
            <FiLayers className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900">
            {t('adminPanel')}
          </span>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        <div className="mb-4">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4">
            {t('navigation') || 'Navigation'}
          </h3>
        </div>
        
        {menuItems.map((item) => {
          const isActive = location.pathname === item.href
          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                'group flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300',
                isActive
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-md'
                  : 'hover:bg-gray-100 text-gray-700 hover:text-gray-900'
              )}
            >
              <item.icon 
                className={cn(
                  'w-5 h-5 transition-transform duration-300',
                  isActive ? 'scale-110' : 'group-hover:scale-110'
                )}
              />
              <span className="flex-1 font-medium">{t(item.labelKey)}</span>
              <FiChevronRight 
                className={cn(
                  'w-4 h-4 transition-all duration-300',
                  isActive 
                    ? 'opacity-100 translate-x-0' 
                    : 'opacity-0 -translate-x-2 group-hover:opacity-100 group-hover:translate-x-0'
                )}
              />
            </Link>
          )
        })}
      </nav>
    </aside>
  )
}