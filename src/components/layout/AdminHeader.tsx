import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import i18next from 'i18next'
import { FiChevronDown, FiGlobe, FiLogOut } from 'react-icons/fi'

export function AdminHeader() {
  const { t } = useTranslation('header')
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isLanguageMenuOpen, setIsLanguageMenuOpen] = useState(false)

  const handleLogout = () => {
    logout()
    navigate('/')
  }

  const changeLanguage = (lng: string) => {
    i18next.changeLanguage(lng)
    setIsLanguageMenuOpen(false)
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="px-6 py-4 flex justify-between items-center">
        {/* Logo */}
        <div className="flex items-center space-x-3">
          <img src="/logo.png" alt={t('logoAlt')} className="h-10 w-auto" />
          <span className="text-gray-500 text-sm font-medium">{t('adminPanel')}</span>
        </div>

        {/* Right side controls */}
        <div className="flex items-center space-x-4">
          {/* Language selector */}
          <div className="relative">
            <button
              onClick={() => setIsLanguageMenuOpen(!isLanguageMenuOpen)}
              className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-all duration-300"
            >
              <FiGlobe className="w-5 h-5" />
              <span className="font-medium text-sm">{i18next.language.toUpperCase()}</span>
            </button>

            {isLanguageMenuOpen && (
              <div className="absolute right-0 mt-2 w-32 bg-white rounded-xl shadow-lg border border-gray-200 py-1 z-50 overflow-hidden">
                <button
                  onClick={() => changeLanguage('tr')}
                  className={`block w-full text-left px-4 py-2 text-sm transition-all duration-300 ${i18next.language === 'tr'
                      ? 'bg-purple-50 text-purple-700'
                      : 'text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  Türkçe
                </button>
                <button
                  onClick={() => changeLanguage('en')}
                  className={`block w-full text-left px-4 py-2 text-sm transition-all duration-300 ${i18next.language === 'en'
                      ? 'bg-purple-50 text-purple-700'
                      : 'text-gray-700 hover:bg-gray-50'
                    }`}
                >
                  English
                </button>
              </div>
            )}
          </div>

          {/* User menu */}
          <div className="relative">
            <button
              onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
              className="flex items-center space-x-3 px-4 py-2 bg-gray-50 border border-gray-200 rounded-xl hover:bg-gray-100 transition-all duration-300"
            >
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold">
                {(user?.storeName || 'Admin').charAt(0).toUpperCase()}
              </div>
              <span className="font-medium text-gray-900">{user?.storeName || 'Admin'}</span>
              <FiChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-300 ${isUserMenuOpen ? 'rotate-180' : ''}`} />
            </button>

            {isUserMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50 overflow-hidden">
                <div className="px-4 py-3 border-b border-gray-200">
                  <p className="text-sm font-semibold text-gray-900">{user?.storeName || 'Admin'}</p>
                  <p className="text-xs text-gray-500 mt-1">{user?.username || '<EMAIL>'}</p>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-all duration-300"
                >
                  <FiLogOut className="w-4 h-4 mr-3" />
                  {t('logout')}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}