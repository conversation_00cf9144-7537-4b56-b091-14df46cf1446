/* Print-specific styles for order confirmation */
@media print {
  /* Hide elements that shouldn't be printed */
  .print\\:hidden {
    display: none !important;
  }

  /* Reset page styles for printing */
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  /* Page setup */
  @page {
    margin: 1in;
    size: A4;
  }

  /* Remove background colors and gradients */
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* Ensure proper text colors for readability */
  h1, h2, h3, h4, h5, h6 {
    color: black !important;
    font-weight: bold;
  }

  /* Remove animations and transitions */
  * {
    animation: none !important;
    transition: none !important;
    transform: none !important;
  }

  /* Adjust spacing for print */
  .space-y-6 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  /* Ensure borders are visible */
  .border {
    border: 1px solid #ccc !important;
  }

  .border-gray-200 {
    border-color: #ccc !important;
  }

  /* Make rounded corners less prominent for print */
  .rounded-xl,
  .rounded-2xl,
  .rounded-3xl {
    border-radius: 8px !important;
  }

  /* Adjust padding for print */
  .p-6 {
    padding: 1rem !important;
  }

  .p-4 {
    padding: 0.75rem !important;
  }

  /* Ensure proper text sizing */
  .text-4xl {
    font-size: 24pt !important;
  }

  .text-3xl {
    font-size: 20pt !important;
  }

  .text-2xl {
    font-size: 16pt !important;
  }

  .text-xl {
    font-size: 14pt !important;
  }

  .text-lg {
    font-size: 13pt !important;
  }

  .text-sm {
    font-size: 10pt !important;
  }

  .text-xs {
    font-size: 9pt !important;
  }

  /* Ensure grid layouts work in print */
  .grid {
    display: block !important;
  }

  .grid > * {
    margin-bottom: 0.5rem !important;
  }

  /* Force page breaks where needed */
  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  /* Specific adjustments for order confirmation */
  .bg-gradient-to-r,
  .bg-gradient-to-br,
  .bg-gradient-to-b {
    background: white !important;
    border: 1px solid #ccc !important;
  }

  /* Ensure icons are visible or replaced with text */
  svg {
    display: none !important;
  }

  /* Add content for important sections */
  .order-section::before {
    content: "■ ";
    font-weight: bold;
  }

  /* Ensure proper spacing between sections */
  .order-item {
    page-break-inside: avoid;
    margin-bottom: 1rem !important;
    border: 1px solid #ccc !important;
    padding: 0.75rem !important;
  }

  /* Make sure totals are prominent */
  .total-section {
    border: 2px solid #000 !important;
    padding: 1rem !important;
    margin: 1rem 0 !important;
    font-weight: bold !important;
  }

  /* Hide decorative elements */
  .animate-pulse,
  .animate-bounce {
    animation: none !important;
  }

  /* Ensure customer information is clearly visible */
  .customer-info {
    border: 1px solid #000 !important;
    padding: 0.75rem !important;
    margin: 0.5rem 0 !important;
  }

  /* Make fabric and configuration details readable */
  .config-details {
    border: 1px solid #ccc !important;
    padding: 0.5rem !important;
    margin: 0.25rem 0 !important;
  }

  /* Ensure proper table-like layout for details */
  .detail-row {
    display: flex !important;
    justify-content: space-between !important;
    border-bottom: 1px dotted #ccc !important;
    padding: 0.25rem 0 !important;
  }

  /* Header styling for print */
  .print-header {
    text-align: center !important;
    border-bottom: 2px solid #000 !important;
    padding-bottom: 1rem !important;
    margin-bottom: 1rem !important;
  }

  /* Footer for print */
  .print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 10pt;
    border-top: 1px solid #ccc;
    padding-top: 0.5rem;
  }
}
