import { create } from 'zustand'
import { User } from '@/types'
import { api } from '@/services/api'
import { toast } from 'react-toastify'

type AuthState = {
  user: User | null
  loading: boolean
  initialized: boolean
  checkAuth: () => Promise<void>
  login: (username: string, password: string) => Promise<any>
  logout: () => void
}

export const useAuth = create<AuthState>((set) => ({
  user: null,
  loading: false,
  initialized: false,

  checkAuth: async () => {
    if (!localStorage.getItem('token')) {
      set({ loading: false, initialized: true })
      return
    }

    try {
      set({ loading: true })
      const response = await api.get('/auth/Me')
      set({ user: response.data })
    } catch (error) {
      console.error('Error fetching user data:', error)
      set({ user: null })
      localStorage.removeItem('token')
    } finally {
      set({ loading: false, initialized: true })
    }
  },

  login: async (username: string, password: string) => {
    try {
      set({ loading: true })
      const response = await api.post('/auth/Login', { username, password })
      localStorage.setItem('token', response.data.token)
      const authResponse = await api.get('/auth/Me')
      set({ user: authResponse.data })
      toast.success('Logged in successfully')
      return response.data
    } catch (error) {
      toast.error('Invalid credentials')
      throw error
    } finally {
      set({ loading: false })
    }
  },

  logout: () => {
    set({ user: null })
    localStorage.removeItem('token')
    toast.info('Logged out')
    window.location.href = '/'
  }
}))