import { create } from 'zustand'
import { toast } from 'react-toastify'
import { api } from '@/services/api'
import i18next from 'i18next'

export interface CartItem {
  id?: string
  design: {
    legs: string
    baseFrame: string
    woodVeneer: string
    woodVeneerFabric: {
      option: string
      color: string
    },
    legFabric: {
      option: string
      color: string
    },
    armrest: {
      main: string
      sub: string
      mainAhsap: boolean
    }
    seat: {
      option: string
      sub: string
    }
    sharedFabric: {
      option: string
      color: string
    }
    mainAhsapFabric: {
      option: string
      color: string
    }
    model: string
    armchairType?: string
    backPillow?: string
    cushions?: Array<{
      cushionType: string
      fabric: string
      color: string
    }>
    customerName: string
    customerSurname: string
    totalPrice: number
    barcode: string
  }
  quantity: number
}

interface CartState {
  cartItems: CartItem[]
  fetchCart: () => Promise<void>
  addToCart: (item: any) => Promise<void>
  removeFromCart: (id: string) => Promise<void>
  updateQuantity: (id: string, quantity: number) => Promise<void>
  clearCart: () => Promise<void>
}

export const useCart = create<CartState>((set, get) => ({
  cartItems: [],

  fetchCart: async () => {
    try {
      const response = await api.get('/Orders/cart')
      if (response.data && response.data.items) {
        set({ cartItems: response.data.items })
      }
    } catch (error) {
      console.error('Error fetching cart:', error)
      set({ cartItems: [] }) // Reset cart on error
    }
  },

    addToCart: async (item: any) => {
    const t = i18next.getFixedT(null, 'header')

    // This method is for simple product additions, not custom designs
    // For now, we'll create a basic design structure
    const basicDesign = {
      legs: "1 Ahşap",
      baseFrame: "1",
      woodVeneer: "1",
      armrest: {
        main: "KOL 1",
        sub: "BOŞ",
        mainAhsap: false
      },
      seat: {
        option: "1",
        sub: "BOŞ"
      },
      sharedFabric: {
        option: "cartela1",
        color: "1"
      },
      woodVeneerFabric: {
        option: "1",
        color: "CevizAhsap"
      },
      legFabric: {
        option: "1 Ahşap",
        color: "CevizAhsap"
      },
      mainAhsapFabric: {
        option: "1",
        color: "CevizAhsap"
      },
      model: item.name || "Custom",
      armchairType: "armchair",
      backPillow: "Silikon Elyaf",
      cushions: [],
      customerName: "Store",
      customerSurname: "Customer",
      totalPrice: item.price || 0,
      barcode: `STORE-${Date.now()}`
    }

    const newItem = {
      design: basicDesign,
      quantity: item.quantity || 1
    }

    try {
      await api.post('/Orders/cart', newItem)
      toast.success(t('addedToCart'))
      await get().fetchCart() // Fetch updated cart data
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error(t('errors.failedToAddItem'))
    }
  },

  removeFromCart: async (id: string) => {
    const t = i18next.getFixedT(null, 'header')
    try {
      await api.delete(`/Orders/cart/${id}`)
      toast.info(t('itemRemovedFromCart'))
      await get().fetchCart() // Fetch updated cart data
    } catch (error) {
      console.error('Error removing from cart:', error)
      toast.error(t('errors.failedToRemoveItem'))
    }
  },

  updateQuantity: async (id: string, quantity: number) => {
    const t = i18next.getFixedT(null, 'header')
    if (quantity < 1) {
      toast.error(t('errors.invalidQuantity'))
      return
    }

    try {
      await api.put(`/Orders/cart/${id}`, { quantity })
      toast.success(t('quantityUpdated'))
      await get().fetchCart() // Fetch updated cart data
    } catch (error) {
      console.error('Error updating quantity:', error)
      toast.error(t('errors.failedToUpdateQuantity'))
    }
  },

  clearCart: async () => {
    const t = i18next.getFixedT(null, 'header')
    try {
      await api.delete('/Orders/cart')
      toast.info(t('cartCleared'))
      set({ cartItems: [] })
    } catch (error) {
      console.error('Error clearing cart:', error)
      toast.error(t('errors.failedToClearCart'))
    }
  }
}))