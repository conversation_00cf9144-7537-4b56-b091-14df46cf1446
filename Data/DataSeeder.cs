using backend.Models;
using backend.Data;
using MongoDB.Driver;

namespace backend.Data
{
  public static class DataSeeder
  {
    public static void SeedData(MongoDbContext context)
    {
      var users = context.Users.Find(_ => true).ToList();
      if (!users.Any())
      {
        var adminUser = new User
        {
          Username = "<EMAIL>",
          PasswordHash = BCrypt.Net.BCrypt.HashPassword("AdminPassword"),
          Role = "admin"
        };
        var storeUser = new User
        {
          Username = "<EMAIL>",
          PasswordHash = BCrypt.Net.BCrypt.HashPassword("StorePassword"),
          Role = "store",
          StoreName = "Example Store"
        };
        context.Users.InsertMany(new[] { adminUser, storeUser });

        // Create store settings for the store user
        var storeSettings = new StoreSetting
        {
          StoreId = storeUser.Id!,
          CompanyName = "Example Store",
          TaxNumber = "*********",
          OwnerName = "Store Owner",
          OwnerPhone = "555-1234",
          StoreAddress = "123 Store St",
          StorePhone = "555-5678",
          ManagerName = "Store Manager",
          ManagerPhone = "555-8765",
          ShippingCompany = "FastShip",
          ShippingPhone = "555-0000",
          ProfitMargin = 20.0m
        };
        context.StoreSettings.InsertOne(storeSettings);
        Console.WriteLine("Seeded Users and Settings.");
      }
    }
  }
}
