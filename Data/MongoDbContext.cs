using Microsoft.Extensions.Configuration;
using MongoDB.Driver;
using backend.Models;

namespace backend.Data
{
    public class MongoDbContext
    {
        private readonly IMongoDatabase _database;

        public MongoDbContext(IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("MongoDbConnection");
            var client = new MongoClient(connectionString);
            var databaseName = MongoUrl.Create(connectionString).DatabaseName;
            _database = client.GetDatabase(databaseName);
        }

        public IMongoCollection<User> Users => _database.GetCollection<User>("Users");
        public IMongoCollection<Furniture> Furnitures => _database.GetCollection<Furniture>("Furnitures");
        public IMongoCollection<Order> Orders => _database.GetCollection<Order>("Orders");
        public IMongoCollection<StoreSetting> StoreSettings => _database.GetCollection<StoreSetting>("StoreSettings");
        public IMongoCollection<FurniturePart> FurnitureParts => _database.GetCollection<FurniturePart>("FurnitureParts");
        public IMongoCollection<PricingData> PricingData => _database.GetCollection<PricingData>("PricingData");
        public IMongoCollection<Cart> Carts => _database.GetCollection<Cart>("Carts");
        public IMongoCollection<SavedDesign> SavedDesigns => _database.GetCollection<SavedDesign>("SavedDesigns");
    }
}
