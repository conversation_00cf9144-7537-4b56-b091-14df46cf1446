# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

```bash
# Build the project
dotnet build

# Run the application (defaults to http://localhost:5030)
dotnet run

# Run with HTTPS profile
dotnet run --launch-profile https

# Restore NuGet packages
dotnet restore

# Clean build artifacts
dotnet clean
```

## Architecture Overview

This is a .NET 8.0 Web API backend for Kapsul Mobilya (furniture customization platform) following clean architecture principles:

### Core Architecture Patterns

1. **Repository Pattern**: Generic `IRepository<T>` interface with MongoDB implementations for each entity
2. **Service Layer**: Business logic is encapsulated in services (e.g., `UserService`)
3. **Dependency Injection**: All dependencies configured in `Program.cs`
4. **RESTful API**: Controllers follow REST conventions with `/api/[controller]` routing

### Key Components

- **Controllers**: Handle HTTP requests, validate input, return appropriate responses
- **Services**: Implement business logic, coordinate between repositories
- **Repositories**: Data access layer, abstract MongoDB operations
- **DTOs**: Data transfer objects for API requests/responses
- **Models**: Domain entities mapped to MongoDB collections

### Database Structure

MongoDB collections managed through `MongoDbContext`:
- Users (authentication, roles)
- Furnitures (furniture models)
- FurnitureParts (customizable parts)
- Orders (customer orders)
- Carts (shopping carts)
- SavedDesigns (user-saved configurations)
- PricingData (pricing rules)
- StoreSettings (global settings)

### Authentication & Security

- JWT Bearer token authentication
- Role-based authorization
- Password hashing with BCrypt
- CORS configured for specific origins

### API Documentation

Swagger/OpenAPI available at `/swagger` when running in development mode.