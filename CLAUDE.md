# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start Vite development server with HMR
- `npm run build` - Build for production (TypeScript + Vite)
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint with TypeScript support
- `npm run format` - Format code with Prettier

## Architecture

This is a React 19 + TypeScript 5.8 furniture customization e-commerce platform with 3D visualization capabilities.

### Core Technologies
- **Build Tool**: Vite 6.3
- **3D Rendering**: Babylon.js 8.3 for furniture model visualization
- **State Management**: Zustand 5.0 (auth, cart) + React Query 5.74 (server state)
- **Routing**: React Router DOM 7.5 with role-based protection
- **Styling**: Tailwind CSS 4.1 + Radix UI/Headless UI components
- **Forms**: React Hook Form 7.55 + Zod validation
- **i18n**: i18next with React bindings (Turkish/English)

### Project Structure
- **Pages are organized by role**: `/pages/admin/*`, `/pages/store/*`, `/pages/common/*`
- **Components follow feature grouping**: `/components/admin/*`, `/components/store/*`, `/components/common/*`, `/components/layout/*`
- **3D models use GLB format** stored in `/public/assets/models/` organized by furniture type
- **Translations** in `/public/locales/{en,tr}/*.json` organized by feature

### Key Patterns
1. **Authentication**: JWT-based with Zustand store (`useAuth` hook) and protected routes
2. **API Integration**: Centralized through `/services/api.ts` with Axios interceptors, targeting `http://localhost:5030/api`
3. **3D Model Loading**: BabylonScene component handles GLB model rendering with texture application
4. **Form Handling**: React Hook Form with Zod schemas for validation
5. **Error Handling**: ErrorBoundary component for graceful failures

### Development Notes
- 3D models have been migrated from OBJ/MTL to GLB format for better performance
- The app supports two user roles: admin and store
- Shopping cart state syncs with backend API
- i18n is configured with browser language detection
- All API calls include JWT token in Authorization header when authenticated