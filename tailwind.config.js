/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#7e22ce', // Purple-600
          dark: '#6b21a8', // Purple-700
          light: '#a855f7', // Purple-500
          foreground: '#FFFFFF',
        },
        secondary: {
          DEFAULT: '#db2777', // Pink-600
          dark: '#be185d', // Pink-700
          light: '#ec4899', // Pink-500
          foreground: '#FFFFFF',
        },
        accent: {
          DEFAULT: '#f3e8ff', // Purple-100
          foreground: '#7e22ce', // Purple-600
        },
        background: '#FFFFFF',
        foreground: '#111827', // Gray-900
        gradient: {
          purple: '#7e22ce',
          pink: '#db2777',
          purpleLight: '#a855f7',
          pinkLight: '#ec4899',
        },
      },
      fontFamily: {
        sans: ['Roboto', 'sans-serif'],
        serif: ['Playfair Display', 'serif'],
      },
      boxShadow: {
        'furniture': '0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 3px -1px rgba(0, 0, 0, 0.03)', // Lighter shadow
      },
      borderRadius: {
        'furniture': '0.75rem',
      },
      gridTemplateColumns: {
        '24': 'repeat(24, minmax(0, 1fr))',
      },
      animation: {
        'gradient': 'gradient 15s ease infinite',
      },
      keyframes: {
        gradient: {
          '0%, 100%': {
            'background-position': '0% 50%',
          },
          '50%': {
            'background-position': '100% 50%',
          },
        },
      },
    },
  },
  plugins: [],
}