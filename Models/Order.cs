// Models/Order.cs

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace backend.Models
{
    public class Order
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; } = null!;

        public string UserId { get; set; } = null!;

        public DateTime OrderDate { get; set; }

        public string Status { get; set; } = null!;

        public decimal TotalPrice { get; set; }

        public virtual string? StoreName { get; set; }

        public List<OrderItem> Items { get; set; } = new List<OrderItem>();
    }

    public class OrderItem
    {
        public int Quantity { get; set; }
        public string? DesignJson { get; set; }
    }
}
