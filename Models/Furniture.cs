using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace backend.Models
{
  public class Furniture
  {
    public string? Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public decimal Price { get; set; }
    public string Category { get; set; } = string.Empty;
    public string? ImageUrl { get; set; }
    public string? FabricId { get; set; }
    public string? Color { get; set; }
    public string? TextureUrl { get; set; }
  }

  /* public class Fabric
   {
     [BsonId]
     [BsonRepresentation(BsonType.ObjectId)]
     public string Id { get; set; }

     [Required]
     [BsonElement("Name")]
     public string Name { get; set; }

     [BsonElement("Color")]
     public string Color { get; set; }

     [BsonElement("TextureUrl")]
     public string TextureUrl { get; set; }
   }*/
}
