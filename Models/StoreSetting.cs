using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace backend.Models
{
    public class StoreSetting
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }

        [BsonRepresentation(BsonType.ObjectId)]
        public string? StoreId { get; set; }

        [Required]
        public string CompanyName { get; set; } = null!;

        public string? TaxNumber { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhone { get; set; }
        public string? StoreAddress { get; set; }
        public string? StorePhone { get; set; }
        public string? ManagerName { get; set; }
        public string? ManagerPhone { get; set; }
        public string? ShippingCompany { get; set; }
        public string? ShippingPhone { get; set; }
        public decimal ProfitMargin { get; set; }
    }
}
