using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace backend.Models
{
  public class SavedDesign
  {
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; }

    public string? UserId { get; set; }

    public DateTime SavedDate { get; set; }

    public string? Name { get; set; }

    public string? CustomerName { get; set; }

    public string? CustomerSurname { get; set; }

    public string? Barcode { get; set; }

    public decimal TotalPrice { get; set; }

    public string? Type { get; set; }

    public string? ArmchairType { get; set; }

    public string? Legs { get; set; }

    public string? BaseFrame { get; set; }

    public string? WoodVeneer { get; set; }

    public ArmrestDetails? Armrest { get; set; }

    public SeatDetails? Seat { get; set; }

    public FabricDetails? SharedFabric { get; set; }

    public FabricDetails? WoodVeneerFabric { get; set; }

    public FabricDetails? LegFabric { get; set; }

    public FabricDetails? MainAhsapFabric { get; set; }

    public string? Model { get; set; }

    public string? BackPillow { get; set; }

    public CushionDetails[]? Cushions { get; set; }
  }

  public class ArmrestDetails
  {
    public string? Main { get; set; }
    public string? Sub { get; set; }
    public bool MainAhsap { get; set; }
  }

  public class SeatDetails
  {
    public string? Option { get; set; }
    public string? Sub { get; set; }
  }

  public class FabricDetails
  {
    public string? Option { get; set; }
    public string? Color { get; set; }
  }

  public class CushionDetails
  {
    public string? CushionType { get; set; }
    public string? Fabric { get; set; }
    public string? Color { get; set; }
  }
}
