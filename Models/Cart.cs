using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace backend.Models
{
  public class Cart
  {
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = ObjectId.GenerateNewId().ToString();

    public string UserId { get; set; } = null!;

    public List<CartItem> Items { get; set; } = new List<CartItem>();
  }

  public class CartItem
  {
    public string Id { get; set; } = ObjectId.GenerateNewId().ToString();
    public Design Design { get; set; } = null!;
    public int Quantity { get; set; }
  }

  public class Design
  {
    public string Legs { get; set; } = null!;
    public string BaseFrame { get; set; } = null!;
    public string WoodVeneer { get; set; } = null!;
    public Armrest Armrest { get; set; } = null!;
    public Seat Seat { get; set; } = null!;
    public Fabric SharedFabric { get; set; } = null!;
    public Fabric WoodVeneerFabric { get; set; } = null!;
    public Fabric LegFabric { get; set; } = null!;
    public Fabric MainAhsapFabric { get; set; } = null!;
    public string Model { get; set; } = null!;
    public string? ArmchairType { get; set; }
    public string? BackPillow { get; set; }
    public List<Cushion>? Cushions { get; set; }
    public string CustomerName { get; set; } = null!;
    public string CustomerSurname { get; set; } = null!;
    public decimal TotalPrice { get; set; }
    public string Barcode { get; set; } = null!;
  }

  public class Armrest
  {
    public string Main { get; set; } = null!;
    public string Sub { get; set; } = null!;
    public bool MainAhsap { get; set; }
  }

  public class Seat
  {
    public string Option { get; set; } = null!;
    public string Sub { get; set; } = null!;
  }

  public class Fabric
  {
    public string Option { get; set; } = null!;
    public string Color { get; set; } = null!;
  }

  public class Cushion
  {
    public string CushionType { get; set; } = null!;
    public string Fabric { get; set; } = null!;
    public string Color { get; set; } = null!;
  }
}
