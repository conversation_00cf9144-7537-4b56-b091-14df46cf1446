{"userCreation": "User Creation", "createNewStoreUser": "Create New Store User", "usernameEmail": "Username (Email)", "password": "Password", "creating": "Creating...", "createUser": "Create User", "invalidEmail": "Invalid email address", "usernameMinLength": "Username must be at least 3 characters long", "passwordMinLength": "Password must be at least 8 characters long", "failedToCreateUser": "Failed to create user", "errorCreatingUser": "Error creating user", "storeName": "Store Name", "storeNameRequired": "Store name is required", "userCreatedSuccessfully": "User created successfully", "storeNamePlaceholder": "Enter store name", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "At least 8 characters"}