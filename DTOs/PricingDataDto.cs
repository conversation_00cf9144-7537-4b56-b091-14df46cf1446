using System.Collections.Generic;
using backend.Models;

namespace backend.DTOs
{
    public class PricingDataDto
    {
        // General Settings - Fixed Costs
        public decimal GeneralOutcomeArmchair { get; set; }
        public decimal GeneralOutcomeBergere { get; set; }
        public decimal ProfitRate { get; set; }
        public decimal FixedFabricAmount { get; set; }

        // Seat Options (1=Çektirme, 3=Tek Parça, 4=Bomba)
        public Dictionary<string, PriceAndFabric> SeatOptions { get; set; } = new Dictionary<string, PriceAndFabric>();

        // Armrest Base Options (1-5)
        public Dictionary<string, PriceAndFabric> ArmrestBase { get; set; } = new Dictionary<string, PriceAndFabric>();

        // Lower Frame (Alt Kasa) - Fixed price + Color-based pricing
        public decimal LowerFrameFixedPrice { get; set; }
        public Dictionary<string, decimal> LowerFrame { get; set; } = new Dictionary<string, decimal>();

        // Leg Options - Each leg type has prices for each color
        public Dictionary<string, Dictionary<string, decimal>> LegOptions { get; set; } = new Dictionary<string, Dictionary<string, decimal>>();

        // Back Pillows & Cushions
        public Dictionary<string, PriceAndFabric> BackPillows { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> Cushions { get; set; } = new Dictionary<string, PriceAndFabric>();

        // Bergere Specific Options
        public Dictionary<string, PriceAndFabric> BergereOptions { get; set; } = new Dictionary<string, PriceAndFabric>();

        // Fabric Prices - Only Kartela 1 with 9 colors
        public Dictionary<string, Dictionary<string, decimal>> Fabrics { get; set; } = new Dictionary<string, Dictionary<string, decimal>>();

        // Legacy fields for backwards compatibility
        public decimal FixedCost { get; set; }
        public Dictionary<string, PriceAndFabric> SeatWithNoEar { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> SeatWithEar { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> Arms { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> ArmExtensions { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> SeatSubOptions { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> ArmrestPapel { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, PriceAndFabric> ArmrestKlapa { get; set; } = new Dictionary<string, PriceAndFabric>();
        public Dictionary<string, decimal> LowerFrameColors { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, decimal> LegColors { get; set; } = new Dictionary<string, decimal>();
    }
}