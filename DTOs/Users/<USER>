using System.ComponentModel.DataAnnotations;

namespace backend.DTOs.Users
{
  public class UserCreateDto
  {
    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string Username { get; set; } = null!;

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = null!;

    [Required]
    [StringLength(50, MinimumLength = 3)]
    public string StoreName { get; set; } = null!;
    
    [EmailAddress]
    public string? Email { get; set; }
  }
}
