using System.ComponentModel.DataAnnotations;

namespace backend.DTOs.Users
{
  public class UserUpdateDto
  {
    [Required]
    [EmailAddress]
    public string Username { get; set; } = null!;
    
    public string? StoreName { get; set; }
    public bool IsActive { get; set; } = true;
    public string Role { get; set; } = "store";
    
    // Basic contact info
    public string? Email { get; set; }
  }
}
