// Repositories/StoreSettingRepository.cs
using backend.Data;
using backend.Models;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Repositories
{
  public class StoreSettingRepository : IRepository<StoreSetting>
  {
    private readonly IMongoCollection<StoreSetting> _settings;

    public StoreSettingRepository(MongoDbContext context)
    {
      _settings = context.StoreSettings;
    }

    public async Task<IEnumerable<StoreSetting>> GetAllAsync()
    {
      return await _settings.Find(setting => true).ToListAsync();
    }

    public async Task<StoreSetting?> GetByIdAsync(string id)
    {
      return await _settings.Find(setting => setting.Id == id).FirstOrDefaultAsync();
    }

    public async Task<StoreSetting?> GetAsync(string key, object value)
    {
      var filter = Builders<StoreSetting>.Filter.Eq(key, value);
      return await _settings.Find(filter).FirstOrDefaultAsync();
    }

    public async Task CreateAsync(StoreSetting entity)
    {
      await _settings.InsertOneAsync(entity);
    }

    public async Task UpdateAsync(string id, StoreSetting entity)
    {
      await _settings.ReplaceOneAsync(setting => setting.Id == id, entity);
    }

    public async Task DeleteAsync(string id)
    {
      await _settings.DeleteOneAsync(setting => setting.Id == id);
    }
  }
}
