using backend.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Repositories
{
  public interface IOrderRepository : IRepository<Order>
  {
    Task<Cart> GetCartAsync(string userId);
    Task<Cart> AddToCartAsync(string userId, CartItem item);
    Task<Cart> RemoveFromCartAsync(string userId, string itemId);
    Task<Cart> UpdateCartItemQuantityAsync(string userId, string itemId, int quantity);
    Task ClearCartAsync(string userId);
    Task<DashboardStats> GetDashboardStatsAsync();
    Task<IEnumerable<SavedDesign>> GetSavedDesignsAsync(string userId);
    Task<SavedDesign> GetSavedDesignByIdAsync(string id);
    Task SaveDesignAsync(string userId, SavedDesign design);
    Task DeleteSavedDesignAsync(string id);
    Task UpdateSavedDesignAsync(string id, SavedDesign design);
  }
}
