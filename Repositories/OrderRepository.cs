using backend.Data;
using backend.Models;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System;

namespace backend.Repositories
{
  public class OrderRepository : IOrderRepository
  {
    private readonly IMongoCollection<Order> _orders;
    private readonly IMongoCollection<Cart> _carts;
    private readonly IMongoCollection<SavedDesign> _savedDesigns;
    private readonly IRepository<User> _users;

    public OrderRepository(MongoDbContext context, IRepository<User> users)
    {
      _orders = context.Orders;
      _carts = context.Carts;
      _savedDesigns = context.SavedDesigns;
      _users = users;
    }

    public async Task<IEnumerable<Order>> GetAllAsync()
    {
      var ordersList = await _orders.Find(order => true).ToListAsync();
      return ordersList.OrderByDescending(o => o.OrderDate)
                      .Select(o =>
                      {
                        var user = _users.GetByIdAsync(o.UserId).Result;
                        return new Order
                        {
                          Id = o.Id,
                          UserId = o.UserId,
                          OrderDate = o.OrderDate,
                          Status = o.Status,
                          TotalPrice = o.TotalPrice,
                          Items = o.Items,
                          StoreName = user?.StoreName
                        };
                      })
                      .ToList();
    }

    public async Task<Order?> GetByIdAsync(string id)
    {
      return await _orders.Find(order => order.Id == id).FirstOrDefaultAsync();
    }

    public async Task<Order?> GetAsync(string key, object value)
    {
      var filter = Builders<Order>.Filter.Eq(key, value);
      return await _orders.Find(filter).FirstOrDefaultAsync();
    }

    public async Task CreateAsync(Order entity)
    {
      await _orders.InsertOneAsync(entity);
    }

    public async Task UpdateAsync(string id, Order entity)
    {
      await _orders.ReplaceOneAsync(order => order.Id == id, entity);
    }

    public async Task DeleteAsync(string id)
    {
      await _orders.DeleteOneAsync(order => order.Id == id);
    }

    public async Task<Cart> GetCartAsync(string userId)
    {
      return await _carts.Find(c => c.UserId == userId).FirstOrDefaultAsync();
    }
    public async Task<Cart> AddToCartAsync(string userId, CartItem item)
    {
      var cart = await GetCartAsync(userId);
      if (cart == null)
      {
        cart = new Cart { UserId = userId, Items = new List<CartItem>() };
      }

      var existingItem = cart.Items.FirstOrDefault(i => i.Design.Barcode == item.Design.Barcode && i.Design.CustomerName == item.Design.CustomerName);
      if (existingItem != null)
      {
        existingItem.Quantity += 1;
      }
      else
      {
        cart.Items.Add(item);
      }

      await _carts.ReplaceOneAsync(c => c.UserId == userId, cart, new ReplaceOptions { IsUpsert = true });
      return await GetCartAsync(userId);
    }

    public async Task<Cart> RemoveFromCartAsync(string userId, string itemId)
    {
      var cart = await GetCartAsync(userId);
      if (cart != null)
      {
        cart.Items.RemoveAll(i => i.Id == itemId);
        await _carts.ReplaceOneAsync(c => c.UserId == userId, cart);
      }
      return await GetCartAsync(userId);
    }

    public async Task<Cart> UpdateCartItemQuantityAsync(string userId, string itemId, int quantity)
    {
      var cart = await GetCartAsync(userId);
      if (cart != null)
      {
        var item = cart.Items.FirstOrDefault(i => i.Id == itemId);
        if (item != null)
        {
          item.Quantity = quantity;
          await _carts.ReplaceOneAsync(c => c.UserId == userId, cart);
        }
      }
      return await GetCartAsync(userId);
    }

    public async Task ClearCartAsync(string userId)
    {
      await _carts.DeleteOneAsync(c => c.UserId == userId);
    }

    public async Task<DashboardStats> GetDashboardStatsAsync()
    {
      try
      {
        var orders = await GetAllAsync();
        var ordersList = orders.ToList();
        var users = (await _users.GetAllAsync()).Where(u => u.Role == "store").ToList();
        return new DashboardStats
        {
          TotalOrders = ordersList.Count,
          TotalUsers = users.Count(),
          TotalIncome = ordersList.Sum(o => o.TotalPrice),
          Orders = ordersList.OrderByDescending(o => o.OrderDate)
                            .Select(o =>
                            {
                              var user = _users.GetByIdAsync(o.UserId).Result;
                              return new Order
                              {
                                Id = o.Id,
                                UserId = o.UserId,
                                OrderDate = o.OrderDate,
                                Status = o.Status,
                                TotalPrice = o.TotalPrice,
                                Items = o.Items,
                                StoreName = user?.StoreName
                              };
                            })
                            .ToList()
        };
      }
      catch (Exception ex)
      {
        // Log the error
        Console.WriteLine($"Error getting dashboard stats: {ex.Message}");

        // Return empty stats rather than throwing
        return new DashboardStats
        {
          TotalOrders = 0,
          TotalUsers = 0,
          TotalIncome = 0,
          Orders = new List<Order>()
        };
      }
    }
    public async Task<IEnumerable<SavedDesign>> GetSavedDesignsAsync(string userId)
    {
      return await _savedDesigns.Find(d => d.UserId == userId).ToListAsync();
    }

    public async Task<SavedDesign> GetSavedDesignByIdAsync(string id)
    {
      return await _savedDesigns.Find(d => d.Id == id).FirstOrDefaultAsync();
    }

    public async Task SaveDesignAsync(string userId, SavedDesign design)
    {
      design.UserId = userId;
      design.SavedDate = DateTime.UtcNow;
      await _savedDesigns.InsertOneAsync(design);
    }

    public async Task DeleteSavedDesignAsync(string id)
    {
      await _savedDesigns.DeleteOneAsync(d => d.Id == id);
    }

    public async Task UpdateSavedDesignAsync(string id, SavedDesign design)
    {
      await _savedDesigns.ReplaceOneAsync(d => d.Id == id, design);
    }
  }
}
