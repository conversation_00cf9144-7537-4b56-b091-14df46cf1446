// Repositories/FurnitureRepository.cs
using backend.Data;
using backend.Models;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Repositories
{
  public class FurnitureRepository : IRepository<Furniture>
  {
    private readonly IMongoCollection<Furniture> _furnitures;

    public FurnitureRepository(MongoDbContext context)
    {
      _furnitures = context.Furnitures;
    }

    public async Task<IEnumerable<Furniture>> GetAllAsync()
    {
      return await _furnitures.Find(Furniture => true).ToListAsync();
    }

    public async Task<Furniture?> GetByIdAsync(string id)
    {
      return await _furnitures.Find(Furniture => Furniture.Id == id).FirstOrDefaultAsync();
    }

    public async Task<Furniture?> GetAsync(string key, object value)
    {
      var filter = Builders<Furniture>.Filter.Eq(key, value);
      return await _furnitures.Find(filter).FirstOrDefaultAsync();
    }

    public async Task CreateAsync(Furniture entity)
    {
      await _furnitures.InsertOneAsync(entity);
    }

    public async Task UpdateAsync(string id, Furniture entity)
    {
      await _furnitures.ReplaceOneAsync(Furniture => Furniture.Id == id, entity);
    }

    public async Task DeleteAsync(string id)
    {
      await _furnitures.DeleteOneAsync(Furniture => Furniture.Id == id);
    }
  }
}
