// Repositories/IRepository.cs
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Repositories
{
    public interface IRepository<T>
    {
        Task<IEnumerable<T>> GetAllAsync();
        Task<T?> GetByIdAsync(string id);
        Task<T?> GetAsync(string key, object value);
        Task CreateAsync(T entity);
        Task UpdateAsync(string id, T entity);
        Task DeleteAsync(string id);
    }
}
