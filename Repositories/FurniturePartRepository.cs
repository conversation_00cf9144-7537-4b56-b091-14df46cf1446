using backend.Data;
using backend.Models;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace backend.Repositories
{
  public class FurniturePartRepository : IRepository<FurniturePart>
  {
    private readonly IMongoCollection<FurniturePart> _furnitureParts;

    public FurniturePartRepository(MongoDbContext context)
    {
      _furnitureParts = context.FurnitureParts;
    }

    public async Task<IEnumerable<FurniturePart>> GetAllAsync()
    {
      return await _furnitureParts.Find(_ => true).ToListAsync();
    }

    public async Task<FurniturePart> GetByIdAsync(string id)
    {
      return await _furnitureParts.Find(x => x.Id == id).FirstOrDefaultAsync();
    }

    public async Task<FurniturePart> GetAsync(string key, object value)
    {
      var filter = Builders<FurniturePart>.Filter.Eq(key, value);
      return await _furnitureParts.Find(filter).FirstOrDefaultAsync();
    }

    public async Task CreateAsync(FurniturePart entity)
    {
      await _furnitureParts.InsertOneAsync(entity);
    }

    public async Task UpdateAsync(string id, FurniturePart entity)
    {
      await _furnitureParts.ReplaceOneAsync(x => x.Id == id, entity);
    }

    public async Task DeleteAsync(string id)
    {
      await _furnitureParts.DeleteOneAsync(x => x.Id == id);
    }
  }
}