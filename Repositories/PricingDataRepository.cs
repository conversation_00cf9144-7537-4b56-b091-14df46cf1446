// Repositories/PricingDataRepository.cs
using backend.Data;
using backend.Models;
using backend.Repositories;
using MongoDB.Driver;
using System.Threading.Tasks;

namespace backend.Repositories
{
  public class PricingDataRepository : ISingletonRepository<PricingData>
  {
    private readonly IMongoCollection<PricingData> _pricingData;

    public PricingDataRepository(MongoDbContext context)
    {
      _pricingData = context.PricingData;
    }

    public async Task<PricingData?> GetAsync()
    {
      try
      {
        return await _pricingData.Find(_ => true).FirstOrDefaultAsync();
      }
      catch (System.FormatException)
      {
        // If deserialization fails due to incorrect data structure, return null
        // This allows the reset endpoint to work even with corrupted data
        return null;
      }
    }

    public async Task UpdateAsync(PricingData entity)
    {
      var filter = Builders<PricingData>.Filter.Empty; // Matches all documents
      await _pricingData.ReplaceOneAsync(filter, entity, new ReplaceOptions { IsUpsert = true });
    }

    public async Task DeleteAllAsync()
    {
      // Delete all pricing data documents
      await _pricingData.DeleteManyAsync(_ => true);
    }
  }
}
