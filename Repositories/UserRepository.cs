// Repositories/UserRepository.cs
using backend.Data;
using backend.Models;
using MongoDB.Driver;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace backend.Repositories
{
    public class UserRepository : IRepository<User>
    {
        private readonly IMongoCollection<User> _users;

        public UserRepository(MongoDbContext context)
        {
            _users = context.Users;
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            return await _users.Find(user => true).ToListAsync();
        }

        public async Task<User?> GetByIdAsync(string id)
        {
            return await _users.Find(user => user.Id == id).FirstOrDefaultAsync();
        }

        public async Task<User?> GetAsync(string key, object value)
        {
            var filter = Builders<User>.Filter.Eq(key, value);
            return await _users.Find(filter).FirstOrDefaultAsync();
        }

        public async Task CreateAsync(User entity)
        {
            await _users.InsertOneAsync(entity);
        }

        public async Task UpdateAsync(string id, User entity)
        {
            await _users.ReplaceOneAsync(user => user.Id == id, entity);
        }

        public async Task DeleteAsync(string id)
        {
            var update = Builders<User>.Update.Set(u => u.IsActive, false);
            await _users.UpdateOneAsync(user => user.Id == id, update);
        }
    }
}
