{"version": "0.2.0", "configurations": [{"name": "Launch Arc - React Vite App", "request": "launch", "type": "chrome", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "runtimeExecutable": "/Applications/Arc.app/Contents/MacOS/Arc", "runtimeArgs": ["--disable-web-security", "--user-data-dir=${workspaceFolder}/.vscode/arc-debug-profile", "--remote-debugging-port=9222"]}, {"name": "Launch Chrome - React Vite App", "request": "launch", "type": "chrome", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src", "sourceMaps": true, "runtimeExecutable": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "runtimeArgs": ["--disable-web-security", "--user-data-dir=${workspaceFolder}/.vscode/chrome-debug-profile"]}, {"name": "Launch Safari - React Vite App", "request": "launch", "type": "safari", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/src"}, {"name": "Attach to <PERSON><PERSON><PERSON>", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}/src", "sourceMaps": true}]}