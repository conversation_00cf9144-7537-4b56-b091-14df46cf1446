{"version": "0.2.0", "configurations": [{"name": "Launch .NET Core Backend", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net8.0/backend.dll", "args": [], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Launch .NET Core Backend with Watch", "type": "coreclr", "request": "launch", "program": "dotnet", "args": ["watch", "run", "--project", "${workspaceFolder}/backend.csproj"], "cwd": "${workspaceFolder}", "console": "internalConsole", "stopAtEntry": false, "internalConsoleOptions": "openOnSessionStart", "env": {"ASPNETCORE_ENVIRONMENT": "Development"}}, {"name": "Attach to .NET Core Process", "type": "coreclr", "request": "attach"}]}