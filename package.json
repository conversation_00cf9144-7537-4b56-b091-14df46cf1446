{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"@babylonjs/core": "^8.3.1", "@babylonjs/inspector": "^8.4.1", "@babylonjs/loaders": "^8.3.1", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.10", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "express": "^5.1.0", "framer-motion": "^12.8.0", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.501.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.1", "react-toastify": "^11.0.5", "tailwind-merge": "^3.2.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-react": "^4.4.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.2"}}